<template>
  <div class="loading-container">
    <!-- 背景渐变 -->
    <div class="background-gradient"></div>
    
        <!-- 主要内容 -->
    <div class="loading-content">
      <!-- 公仔面图片 -->
      <div class="noodle-image">
        <img src="/img/loa.png" alt="公仔面" />
      </div>
      
      <!-- 加载进度条 -->
      <div class="loading-bar-container">
        <div class="loading-bar">
          <div class="loading-progress" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="loading-text">LOADING</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
// @ts-ignore
import {wxAuth} from "@/utils/WxUtils.js"

const router = useRouter()
const progress = ref(0)
let progressInterval = null

// 模拟加载进度
const startLoading = () => {
  progressInterval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += Math.random() * 15 + 5
      if (progress.value > 100) {
        progress.value = 100
      }
    } else {
      clearInterval(progressInterval)
      // 加载完成后跳转到首页
      setTimeout(() => {
        router.push('/home')
      }, 500)
    }
  }, 200)
}

onMounted(async () => {
  // 授权码
  await wxAuth()
  // 开启加载
  startLoading()
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
})
</script>

<style scoped lang="scss">
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ff2985;
}



.loading-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100vh;
  padding: 0 20px;
}

.noodle-image {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  text-align: center;
}

.noodle-image img {
  max-width: 300px;
  max-height: 300px;
  width: auto;
  height: auto;
  object-fit: contain;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
  animation: imageFloat 3s ease-in-out infinite;
}

@keyframes imageFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.noodle-icon {
  position: relative;
  width: 200px;
  height: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 蒸汽动画
.steam {
  position: absolute;
  top: -20px;
  width: 4px;
  height: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: steamRise 2s ease-in-out infinite;
}

.steam-1 {
  left: 60px;
  animation-delay: 0s;
}

.steam-2 {
  right: 60px;
  animation-delay: 0.5s;
}

@keyframes steamRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-30px) scale(0.5);
    opacity: 0;
  }
}

// 叉子
.fork {
  position: absolute;
  top: -10px;
  width: 60px;
  height: 4px;
  background: white;
  border-radius: 2px;
  z-index: 5;
}

.fork::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 20px;
  background: white;
  border-radius: 2px;
}

// 杯面主体
.noodle-cup {
  width: 160px;
  height: 200px;
  background: white;
  border-radius: 20px 20px 30px 30px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: cupFloat 3s ease-in-out infinite;
}

@keyframes cupFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.band {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  position: relative;
}

.band-top {
  background: #ffd700;
  border-radius: 20px 20px 0 0;
}

.band-middle {
  background: #ff4444;
}

.band-bottom {
  background: white;
  border-radius: 0 0 30px 30px;
}

.text-main {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 2px;
}

.text-sub {
  font-size: 8px;
  color: white;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

.band-bottom .text-main {
  color: #ff4444;
  text-shadow: none;
}

// 加载进度条
.loading-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-align: center;
}

.loading-bar {
  width: 300px;
  height: 13px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.loading-progress {
  height: 100%;
  background: white;
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.loading-text {
  color: white;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .noodle-image img {
    max-width: 200px;
    max-height: 200px;
  }
  
  .noodle-icon {
    width: 150px;
    height: 180px;
  }
  
  .noodle-cup {
    width: 120px;
    height: 150px;
  }
  
  .text-main {
    font-size: 14px;
  }
  
  .text-sub {
    font-size: 6px;
  }
  
  .loading-bar {
    width: 250px;
  }
  
  .loading-content {
    gap: 8px;
    padding: 0 15px;
  }
}
</style>
