{"name": "gongzaimian", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "tdesign-mobile-vue": "^1.10.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tdesign-vue-next/auto-import-resolver": "^0.1.1", "@types/node": "^24.2.1", "@vitejs/plugin-vue": "^6.0.0", "sass": "^1.90.0", "sass-loader": "^16.0.5", "typescript": "^5.9.2", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}