<template>
  <div class="logo-container">
    <img
      class="logo-img"
      :class="{ fade: isFading, in: !isFading }"
      :style="logoStyle"
      :key="currentSrc"
      :src="currentSrc"
      alt="主视觉图"
      @load="onImageLoad"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed } from "vue";

const transitionDuration = 4000; // intro GIF播放时间
const fadeDuration = 300; // 淡出时间
const mainSrc = "/img/gzaimian-logo.gif";
const introSrc = "/img/gzaimian-logo2.gif";

const currentSrc = ref(mainSrc);
const isFading = ref(false);
const introScale = ref(1);

let mainNaturalWidth = 0;
let introNaturalWidth = 0;

function onImageLoad(e) {
  const img = e.target;
  const src = img.src;

  if (src.includes(mainSrc)) {
    mainNaturalWidth = img.naturalWidth;
  } else if (src.includes(introSrc)) {
    introNaturalWidth = img.naturalWidth;
  }

  if (mainNaturalWidth && introNaturalWidth) {
    const ratio = mainNaturalWidth / introNaturalWidth;
    introScale.value = Math.max(ratio, 1.4); //  最小放大 1.4 倍
  }
}

// 动态样式：只有在 intro 阶段才放大
const logoStyle = computed(() => {
  if (currentSrc.value === introSrc) {
    return {
      transform: `scale(${introScale.value})`,
      transformOrigin: "center center",
    };
  }
  return {};
});

onMounted(async () => {
  const hasPlayed = sessionStorage.getItem("introPlayed");

  if (!hasPlayed) {
    currentSrc.value = introSrc;
    isFading.value = false;

    setTimeout(async () => {
      isFading.value = true;
      await nextTick();
      setTimeout(() => {
        currentSrc.value = mainSrc;
        isFading.value = false;
        sessionStorage.setItem("introPlayed", "true");
      }, fadeDuration);
    }, transitionDuration);
  }
});
</script>

<style scoped>
.logo-container {
  width: clamp(16rem, 22vw, 24rem);
  aspect-ratio: 1 / 1;
  max-width: 95vw;
  max-height: 85vh;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  overflow: hidden;
}

.logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.fade {
  opacity: 0;
}
.fade.in {
  opacity: 1;
}

@media (max-width: 320px) {
  .logo-container {
    width: 18rem;
  }
}

@media (min-width: 430px) {
  .logo-container {
    width: 20rem;
  }
}
</style>
