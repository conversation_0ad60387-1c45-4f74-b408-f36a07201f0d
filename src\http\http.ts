import axios from 'axios';
import { Toast } from "tdesign-mobile-vue";
import { useRouter } from 'vue-router'
const router = useRouter()

const http = axios.create({
    baseURL: "/api/",
    timeout: 300000,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8'
    }
});

// 请求拦截器
http.interceptors.request.use(
    config => {
        // 添加认证token
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    response => {
        // 处理业务逻辑错误
        if (response.data?.code !== 200) {
            Toast(response.data?.msg || '业务错误');
            return Promise.reject(response.data);
        }
        return response.data;
    },
    error => {
        // 统一错误处理
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    Toast('未授权，请重新登录');
                    router.push('/home');
                    break;
                case 403:
                    Toast('拒绝访问');
                    break;
                case 500:
                    Toast('服务器错误');
                    break;
                default:
                    Toast(`请求错误: ${error.message}`);
            }
        } else {
            // Toast('网络错误，请检查连接');
        }
        return Promise.reject(error);
    }
);

export default http;