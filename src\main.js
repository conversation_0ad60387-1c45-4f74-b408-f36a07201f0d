import '@/assets/main.css'

// 移动端适配 - rem 动态设置
function setRem() {
  const baseSize = 16 // 基准字体大小
  const scale = Math.min(Math.max(document.documentElement.clientWidth / 375, 0.8), 1.2)
  document.documentElement.style.fontSize = baseSize * scale + 'px'
}

// 初始化
setRem()
// 监听窗口变化
window.addEventListener('resize', setRem)
window.addEventListener('orientationchange', setRem)

import { createApp } from 'vue'
import App from '@/App.vue'
import router from '@/router/index.js'

createApp(App).use(router).mount('#app')
