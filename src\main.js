import '@/assets/main.css'
import VConsole from 'vconsole';
const vConsole = new VConsole();
// 移动端适配 - rem 动态设置
function setRem() {
  const baseSize = 16 // 基准字体大小
  const scale = document.documentElement.clientWidth / 375
  document.documentElement.style.fontSize = baseSize * scale + 'px'
}

// 初始化
setRem()
// 监听窗口变化
window.addEventListener('resize', setRem)
window.addEventListener('orientationchange', setRem)

import { createApp } from 'vue'
import App from '@/App.vue'
import router from '@/router/index.js'

createApp(App).use(router).mount('#app')
