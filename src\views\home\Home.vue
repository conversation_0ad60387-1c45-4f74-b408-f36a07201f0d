<script setup>
// @ts-ignore
import {config} from "@/utils/WxUtils.js"
import {wxLogin} from "@/http/wxApi.js";
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import Animated<PERSON><PERSON> from "./AnimatedLogo.vue";
const router = useRouter();
const showRules = ref(false);

const redirectLink = "https://gzm.easyart.cc"
onMounted(async ()=>{
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code'); // 通过参数名获取参数值
  const state = urlParams.get('state'); // 通过参数名获取参数值
  if (code){
    // 调用方法获取openid
    let res = await wxLogin(code)
    if (res.code === 200){
      let data = res.data
      let token = data.token;
      let user = data.user;
      localStorage.setItem("token", token)
      localStorage.setItem("user",JSON.stringify(user))
      window.location.href = redirectLink
    }
  }
  await config()
})

function goToPick() {
  router.push("/pick");
}

function openRules() {
  showRules.value = true;
  // 显示模态框时禁止背景滚动
  document.body.style.overflow = "hidden";
}

function closeRules() {
  showRules.value = false;
  // 关闭模态框时恢复背景滚动
  document.body.style.overflow = "";
}

// 支持ESC键关闭模态框
function handleKeydown(e) {
  if (e.key === 'Escape' && showRules.value) {
    closeRules();
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
});
</script>

<template>
  <div class="container home-background">
    <header class="header">
      <img 
        alt="logo" 
        class="logo" 
        src="/img/logo.png" 
        onerror="this.src='/img/logo-fallback.png'"
      />
    </header>
    <main class="main">
      <AnimatedLogo />
    </main>
    <footer class="footer vertical">
      <button
        class="btn btn-solid start-game"
        @click="goToPick"
        aria-label="开始游戏"
      >
        开始游戏
      </button>
      <button
        class="btn btn-solid game-rules"
        @click="openRules"
        aria-label="游戏规则"
      >
        游戏规则
      </button>
    </footer>
<!-- 博文编写 -->
    <!-- 游戏规则弹窗 -->
    <div v-if="showRules" class="rules-overlay" @click="closeRules">
      <div class="rules-modal" @click.stop>
        <!-- 活动时间 -->
        <div class="rules-section">
          <h2 class="rules-title">活动时间</h2>
          <p class="rules-date">2025年9月15日~2026年2月28日 (待定)</p>
        </div>
        
        <!-- 活动规则 -->
        <div class="rules-section">
          <h2 class="rules-title">活动规则</h2>
          <div class="rules-list">
            <div class="rule-item">
              <span class="rule-number">1</span>
              <span class="rule-text">用户参与任意一项游戏项目，完整体验，并在结果页进行分享时，即可获得随机红包；</span>
            </div>
            <div class="rule-item">
              <span class="rule-number">2</span>
              <span class="rule-text">红包的中奖率为30%，金额分别为0.3元(29.8%)/0.88元(0.1%)/8.8元(0.01%)三种；</span>
            </div>
            <div class="rule-item">
              <span class="rule-number">3</span>
              <span class="rule-text">每个ID每天有3次参与游戏，并抽取红包的机会，如在3次内抽取到红包，则后续不会再获取，如3次都抽不到，只能24小时后再参与；</span>
            </div>
            <div class="rule-item">
              <span class="rule-number">4</span>
              <span class="rule-text">随机红包派发的次数有限，活动期间派完即止，先玩先得。</span>
            </div>
          </div>
        </div>
        
        <!-- 底部说明 -->
        <div class="rules-footer">
          <p class="rules-disclaimer">*活动最终解释权，归公仔碗仔面生产商所有</p>
          <button class="rules-close-btn" @click="closeRules">返回</button>

        </div>
      </div>
    </div>
    <!-- 博文编写 -->
  </div>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  font-family: "PingFang SC", "Helvetica Neue", Arial, "Hiragino Sans GB",
    "Microsoft YaHei", sans-serif;
  overflow: hidden;
  padding: 0;
  /* 安全区域适配 */
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.home-background {
  background: url("/img/gzaimian-bg1.png") no-repeat center center;
  background-size: cover;
  min-height: 100vh;
}
.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 4.5rem;
  margin-bottom: 0.5rem;
  padding: 0 1rem;
}
.logo {
  width: 100%;
  max-width: 200px;
  height: auto;
  display: block;
  object-fit: contain;
}
.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #222;
  letter-spacing: 0.125rem;
}
.main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 0.5rem;
  padding: 0.1rem;
}

.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 1.5rem 1.5rem 1.5rem;
  box-sizing: border-box;
  gap: 1rem;
}
.footer.vertical {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.2rem;
  padding: 1rem 1.5rem 2.5rem 1.5rem;
}
.btn {
  flex: 1;
  margin: 0 0.5rem;
  padding: 0;
  border: none;
  background: none;
  background-position: center;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

/* 纯色按钮样式 */
.btn-solid {
  background: #ff2985;
  border-radius: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  padding: 1rem 2.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 280px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 4px 15px rgba(255, 41, 133, 0.3);
  border: none;
}

.btn-solid:hover {
  background: #e0247a;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 41, 133, 0.4);
}

.btn-solid:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 10px rgba(255, 41, 133, 0.3);
}

.start-game {
  background: #ff2985;
}

.game-rules {
  background: #ff2985;
}

.btn:active {
  opacity: 0.8;
  transform: scale(0.96);
}

/* 小屏幕适配 */
@media (max-width: 320px) {
  .title {
    font-size: 1.8rem;
  }
  .footer {
    padding: 0.25rem 1.5rem 1.25rem 1.5rem;
  }
  .btn-solid {
    font-size: 1rem;
    padding: 0.8rem 2rem;
    max-width: 220px;
    min-height: 50px;
    border-radius: 50px;
  }
}

/* 大屏幕适配 */
@media (min-width: 430px) {
  .title {
    font-size: 2.5rem;
  }

  .btn-solid {
    font-size: 1.4rem;
    padding: 1.2rem 3rem;
    max-width: 320px;
    min-height: 70px;
    border-radius: 50px;
  }
}

/* 游戏规则弹窗样式 */
.rules-overlay {

  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  box-sizing: border-box;
}

.rules-modal {
  background: white;
  border-radius: 30px;
  width: 100%;
  max-width: 350px;
  max-height: 75vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: rulesFadeIn 0.3s ease-out;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rules-section {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.rules-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 900;
  color: #ff2985;
  text-align: center;
}

.rules-date {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  background: #ff2985;
  border-radius: 8px;
  padding: 0.75rem;
  min-height: 60px;
}

.rule-number {
  background: #ffd700;
  color: #333;
  font-weight: bold;
  font-size: 1.2rem;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.rule-text {
  color: white;
  font-size: 0.9rem;
  line-height: 1.4;
  flex: 1;
}

.rules-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
}

.rules-disclaimer {
  margin: 0;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  line-height: 1.3;
}

.rules-close-btn {
  background: white;
  border: 2px solid #ff2985;
  color: #ff2985;
  font-size: 1rem;
  font-weight: 900;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.rules-close-btn:hover {
  background: #ff2985;
  color: white;
}

.rules-close-btn:active {
  transform: scale(0.98);
}
/* 模态框底部按钮区域 */
.modal-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
}

/* 返回按钮样式 */
.back-btn {
  background-color: #ff2a86; 
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.back-btn:hover {
  background-color: #ff2a86;
  transform: translateY(-2px);
}

.back-btn:active {
  transform: translateY(0);
  opacity: 0.9;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 手机适配 - 中等屏幕 */
@media (max-width: 480px) {
  .rules-overlay {
    padding: 0.3rem;
  }

  .rules-modal {
    max-height: 95vh;
    border-radius: 18px;
    padding: 0.8rem;
    gap: 0.6rem;
    max-width: 320px;
    overflow-y: auto;
  }

  .rules-section {
    gap: 0.4rem;
  }

  .rules-title {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
  }

  .rules-date {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .rules-list {
    gap: 0.4rem;
  }

  .rule-item {
    padding: 0.5rem;
    min-height: 40px;
    gap: 0.4rem;
  }

  .rule-number {
    width: 22px;
    height: 22px;
    font-size: 0.9rem;
    margin-top: 1px;
  }

  .rule-text {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .rules-footer {
    gap: 0.6rem;
    margin-top: 0.3rem;
  }

  .rules-close-btn {
    padding: 0.5rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 900;
    min-width: 80px;
  }

  .rules-disclaimer {
    font-size: 0.65rem;
    line-height: 1.2;
  }
}

/* 手机适配 - 小屏幕 */
@media (max-width: 375px) {
  .rules-overlay {
    padding: 0.2rem;
  }

  .rules-modal {
    max-height: 98vh;
    border-radius: 16px;
    padding: 0.6rem;
    gap: 0.5rem;
    max-width: 300px;
    overflow-y: auto;
  }

  .rules-section {
    gap: 0.3rem;
  }

  .rules-title {
    font-size: 1rem;
    margin-bottom: 0.15rem;
  }

  .rules-date {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .rules-list {
    gap: 0.3rem;
  }

  .rule-item {
    padding: 0.4rem;
    min-height: 35px;
    gap: 0.3rem;
  }

  .rule-number {
    width: 20px;
    height: 20px;
    font-size: 0.85rem;
    margin-top: 0.5px;
  }

  .rule-text {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .rules-footer {
    gap: 0.5rem;
    margin-top: 0.2rem;
  }

  .rules-close-btn {
    padding: 0.4rem 1.3rem;
    font-size: 0.85rem;
    font-weight: 900;
    min-width: 70px;
  }

  .rules-disclaimer {
    font-size: 0.6rem;
    line-height: 1.1;
  }
}

/* 手机适配 - 超小屏幕 */
@media (max-width: 320px) {
  .rules-overlay {
    padding: 0.1rem;
  }

  .rules-modal {
    max-height: 99vh;
    border-radius: 14px;
    padding: 0.5rem;
    gap: 0.4rem;
    max-width: 280px;
    overflow-y: auto;
  }

  .rules-section {
    gap: 0.25rem;
  }

  .rules-title {
    font-size: 0.95rem;
    margin-bottom: 0.1rem;
  }

  .rules-date {
    font-size: 0.65rem;
    line-height: 1.1;
  }

  .rules-list {
    gap: 0.25rem;
  }

  .rule-item {
    padding: 0.35rem;
    min-height: 30px;
    gap: 0.25rem;
  }

  .rule-number {
    width: 18px;
    height: 18px;
    font-size: 0.8rem;
    margin-top: 0;
  }

  .rule-text {
    font-size: 0.65rem;
    line-height: 1.1;
  }

  .rules-footer {
    gap: 0.4rem;
    margin-top: 0.15rem;
  }

  .rules-close-btn {
    padding: 0.35rem 1.2rem;
    font-size: 0.8rem;
    font-weight: 900;
    min-width: 65px;
  }

  .rules-disclaimer {
    font-size: 0.55rem;
    line-height: 1.0;
  }
}

/* 横屏手机适配 */
@media (max-width: 768px) and (orientation: landscape) {
  .rules-overlay {
    padding: 0.2rem;
  }

  .rules-modal {
    max-height: 95vh;
    max-width: 450px;
    padding: 0.6rem;
    gap: 0.5rem;
    overflow-y: auto;
  }

  .rules-section {
    gap: 0.3rem;
  }

  .rules-title {
    font-size: 1rem;
    margin-bottom: 0.1rem;
  }

  .rules-date {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  .rules-list {
    gap: 0.3rem;
  }

  .rule-item {
    padding: 0.4rem;
    min-height: 35px;
    gap: 0.3rem;
  }

  .rule-number {
    width: 20px;
    height: 20px;
    font-size: 0.85rem;
    margin-top: 0.5px;
  }

  .rule-text {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .rules-footer {
    gap: 0.4rem;
    margin-top: 0.2rem;
  }

  .rules-close-btn {
    padding: 0.4rem 1.3rem;
    font-size: 0.85rem;
    min-width: 70px;
  }

  .rules-disclaimer {
    font-size: 0.6rem;
    line-height: 1.1;
  }
  
  .modal-footer {
    padding: 0.75rem 1rem 1rem;
    background-color: #ff2a86;
  }
  
.back-btn {
  padding: 0.35rem 1.0rem;
  font-size: 0.9rem;
  color: white; /* 白色字体 */
  border: 0.2rem solid white; /* 0.1rem白色边框 */
  border-radius: 1.2rem; /* 可选：添加圆角使按钮看起来更美观 */
  background-color: #ff2a86; /* 可选：透明背景 */
}
}
</style>
