<template>
  <!-- 蓄力条容器 -->
  <div
    class="charge-container"
  >
    <!-- 蓄力进度条 -->
    <div
      class="charge-bar"
      :style="{
        height: `${props.progress}%`,
      }"
    ></div>
  </div>

  <!-- 进度显示 -->
  <!-- <div class="progress-info">
      <p>当前进度: <span class="current-progress">{{ currentProgress }}%</span></p>
      <p v-if="lastProgress !== null" class="last-progress">上次松开时进度: <span>{{ lastProgress }}%</span></p>
    </div> -->
</template>

<script setup>
const props = defineProps({
  progress: {
    type: [Number, String],
    default: 0
  }
})

</script>

<style scoped>
/* 蓄力条容器样式 */
.charge-container {
  width: 50px;
  height: 256px;
  background-color: #e5e7eb;
  border-radius: 32px;
  position: fixed;
  z-index: 3;
  left: 20px;
  top: 50vh;
  transform: translateY(-50%);
  overflow: hidden;
}

/* 蓄力进度条样式 */
.charge-bar {
  width: 100%;
  background: linear-gradient(to top, #60a5fa, #2563eb);
  border-radius: 32px;
  position: absolute;
  bottom: 0;
  transition: height 0.1s ease;
}

/* 动画效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.active {
  animation: pulse 1s infinite;
}
</style>
