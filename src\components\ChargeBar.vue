<template>
  <div class="charge-container">
    <div class="bar-box">
      <!-- 蓄力进度条 -->
      <div class="charge-bar" :style="{ height: `${props.progress}%` }"></div>
      <img src="@/assets/img/game/charge_line.png" class="charge_line" />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  progress: {
    type: [Number, String],
    default: 0
  }
})

</script>

<style scoped>
/* 蓄力条容器样式 */
.charge-container {
  width: 34px;
  height: 186px;
  background: url('@/assets/img/game/charge.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 32px;
  position: fixed;
  z-index: 3;
  left: 20px;
  top: 50vh;
  transform: translateY(-50%);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bar-box {
  width: 12px;
  height: 120px;
  position: relative;
}

/* 蓄力进度条样式 */
.charge-bar {
  width: 12px;
  background: yellow;
  border-radius: 32px;
  position: absolute;
  bottom: 0;
  transition: height 0.1s ease;
}

.charge_line {
  position: absolute;
  width: 25px;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 动画效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.active {
  animation: pulse 1s infinite;
}
</style>
