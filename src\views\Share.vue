<template>
  <div class="share-bg">
    <!-- 分享图标 -->
    <img src="/img/sharetubiao.png" alt="分享图标" class="share-icon" />

    <!-- 添加cqwangq图片 -->
    <img src="/img/cqwangq.png" alt="彩色文字" class="cqwangq-image" />

    <!-- 添加cqxia图片 -->
    <img src="/img/cqxia.png" alt="虾子角色" class="cqxia-image" />

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="action-btn play-again-btn" @click="playAgain">
        再玩一次
      </button>
      <button class="action-btn share-btn" @click="share">分享</button>
      <button class="action-btn red-packet-btn" @click="drawRedPacket">
        抽取红包
      </button>
      <!--  红包组件（点击“抽取红包”按钮后显示） -->
      <RedEnvelope v-if="showEnvelope" @close="showEnvelope = false" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";

const router = useRouter();
//  引入红包组件
import RedEnvelope from "@/components/RedEnvelope.vue";
import { ref } from "vue";
//  控制红包显示状态
const showEnvelope = ref(false);
//  抽取红包逻辑（点击按钮触发）
function drawRedPacket() {
  showEnvelope.value = true;
}

// 再玩一次
function playAgain() {
  // 这里可以跳转到游戏页面或重新开始游戏
  router.push("/pick");
}

// 分享
function share() {
  // 这里可以调用分享功能
  if (navigator.share) {
    navigator.share({
      title: "公仔面游戏",
      text: "快来和我一起玩公仔面游戏吧！",
      url: window.location.href,
    });
  } else {
    // 复制链接到剪贴板
    copyToClipboard(window.location.href);
    alert("链接已复制到剪贴板！");
  }
}

// 抽取红包
function drawRedPacket() {
  // 这里可以实现抽取红包的逻辑
  alert("红包功能开发中...");
}

// 复制到剪贴板
function copyToClipboard(text) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text);
  } else {
    // 兼容旧浏览器
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
  }
}
</script>

<style scoped>
.share-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100svh;
  min-width: 100svw;
  background: url("/img/share.png") no-repeat center center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  padding: 0;
  margin: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 分享图标 */
.share-icon {
  position: absolute;
  left: 50%;
  top: -15px;
  transform: translateX(-50%);
  width: 200px;
  height: 100px;
  z-index: 10;
  pointer-events: none;
  object-fit: contain;
  margin-left: 100px;
}

/* cqwangq图片 */
.cqwangq-image {
  position: absolute;
  left: 50%;
  top: 27%;
  transform: translate(-50%, -50%);
  width: 1400px;
  height: 560px;
  z-index: 5;
  pointer-events: none;
  object-fit: contain;
}

/* cqxia图片 */
.cqxia-image {
  position: absolute;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  width: 1280px;
  height: 960px;
  z-index: 6;
  pointer-events: none;
  object-fit: contain;
}

/* 底部按钮 */
.bottom-buttons {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 20px;
  z-index: 20;
  width: calc(100% - 80px);
  justify-content: center;
  align-items: center;
}

.action-btn {
  width: 220pt;
  height: 90pt;
  border: none;
  border-radius: 18pt;
  font-size: 24pt;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  flex: 0 1 auto;
}

.play-again-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff2985);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 41, 133, 0.3);
}

.play-again-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 41, 133, 0.4);
}

.share-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff2985);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 41, 133, 0.3);
}

.share-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 41, 133, 0.4);
}

.red-packet-btn {
  background: #fdf500;
  color: #ff0000;
  box-shadow: 0 4px 15px rgba(253, 245, 0, 0.3);
}

.red-packet-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(253, 245, 0, 0.4);
}

.share-container {
  width: 100%;
  max-width: 600px;
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.share-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  text-align: center;
  margin: 1rem 0 2rem 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.share-content {
  width: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn-wrap {
  margin-top: 2rem;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-container {
    padding: 1rem;
  }

  .share-title {
    font-size: 2rem;
  }

  .share-icon {
  }

  .cqwangq-image {
    width: 600px;
    height: 240px;
  }

  .cqxia-image {
    width: 500px;
    height: 375px;
  }

  .bottom-buttons {
    bottom: 30px;
    gap: 12px;
    width: calc(100% - 40px);
  }

  .action-btn {
    width: 100pt;
    height: 45pt;
    font-size: 18pt;
  }
}

@media (max-width: 480px) {
  .share-title {
    font-size: 1.8rem;
  }

  .cqwangq-image {
    width: 700px;
    height: 280px;
  }

  .cqxia-image {
    width: 690px;
    height: 518px;
  }

  .bottom-buttons {
    bottom: 30px;
    gap: 10px;
    width: calc(100% - 40px);
  }

  .action-btn {
    width: 100pt;
    height: 45pt;
    font-size: 16pt;
  }
}
</style>
