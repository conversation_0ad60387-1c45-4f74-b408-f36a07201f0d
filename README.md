# ipgongzaimian

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

# 页面功能与逻辑解读

## 首页 Home.vue
- **功能描述**：
  - H5手机端风格，顶部为logo和"公仔面"名称。
  - 中间展示主视觉图片。
  - 底部有"游戏规则"和"GO"两个按钮。
  - 点击"GO"按钮跳转到卡片选择页面（/pick）。
- **主要逻辑**：
  - 使用vue-router进行页面跳转。
  - 页面布局自适应，视觉简洁美观。

## 卡片选择页面 Pick.vue
- **功能描述**：
  - 顶部为"Pick你喜欢的公仔"标题。
  - 中间为5张卡片的金字塔堆叠轮播，始终保持中间1张、左右各2张。
  - 卡片自动轮播，每2秒切换一次，带有丝滑的滑动动画。
  - 支持手势滑动切换卡片，动画跟手。
  - 点击"就它了"按钮：
    - 如果当前卡片为"呆呆鱼"，跳转到艺术体操小游戏页面（/gym）。
    - 其他卡片弹窗提示。
- **主要逻辑**：
  - 卡片堆叠通过v-for和动态style实现，transform/scale/zIndex/box-shadow等参数控制金字塔效果。
  - 自动轮播和手势滑动均支持丝滑动画，动画收尾无闪烁。
  - 通过vue-router实现页面跳转。

## 艺术体操打地鼠小游戏 RhythmicGymnastics.vue
- **功能描述**：
  - 顶部显示：项目：艺术体操，分数，倒计时。
  - 中间区域：每轮随机出现3个地鼠，分布不重叠。
  - 底部：游戏结束弹窗，显示最终得分。
- **核心玩法**：
  - 每轮（0.8秒）随机出现3个地鼠，点击任意地鼠得分并让该地鼠消失。
  - 0.8秒后所有地鼠消失，0.3秒后新一轮3个地鼠出现。
  - 60秒倒计时，时间到后游戏结束，显示最终得分。
- **动画与体验**：
  - 地鼠出现/消失有滑动和缩放动画，点击反馈灵敏。
  - 地鼠位置每轮随机，且保证3个地鼠之间不会重叠（最小间距90px）。
  - 游戏结束后所有地鼠消失，弹窗提示可重新开始。
- **主要逻辑**：
  - 使用 while 循环生成3个地鼠，每次新地鼠与已生成地鼠的距离大于90px才加入本轮，避免重叠。
  - 使用 v-for 和 transition-group 渲染3个地鼠，地鼠位置通过 style 动态定位。
  - 点击地鼠时，分数+1，并将该地鼠从数组中移除，剩余地鼠不受影响。
  - 每轮0.8秒后所有地鼠消失，0.3秒后自动生成新一轮地鼠。
  - 60秒倒计时，时间到后停止所有定时器，显示"游戏结束"弹窗。
  - 地鼠出现/消失有滑动和缩放动画，提升游戏趣味性和流畅度。

---

如需调整页面内容、动画风格、跳转逻辑等，可在对应组件内灵活修改。
