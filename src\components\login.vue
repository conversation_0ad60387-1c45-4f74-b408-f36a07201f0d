<script setup lang="ts">
import {ref} from "vue";

const redirect_uri = "https://gzm.easyart.cc"
const link = ref(`https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx721efc82a86c51ca&redirect_uri=${redirect_uri}&response_type=code&scope=snsapi_userinfo&state=easyart#wechat_redirect`)

</script>

<template>
<div class="login">
  <div class="mask">
    <div class="pic">
      <img src="/img/logo.png" alt="">
    </div>
    <a class="auth" :href="link">授权登录</a>
  </div>
</div>
</template>

<style scoped lang="scss">
.login{
  position: fixed;
  z-index: 99;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.4);
  .mask{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    width: 300px;
    height: 200px;
    background-color: white;
    border-radius: 20px;
    text-align: center;
    .pic{
      margin-top: 30px;
      margin-bottom: 50px;
      img{
        width: 100px;
        height: 30px;
      }
    }
    .auth{
      display: inline-block;
      width: 60%;
      line-height: 30px;
      background-color: #ff2985;
      color: white;
      border-radius: 30px;
    }
  }
}
</style>