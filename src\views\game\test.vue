<template>
  <div class="gym-container">
    <header class="gym-header gym-header-fixed">
      <span>项目：{{ projectName }}</span>
      <span class="score-display">分数：{{ score }}</span>
      <span>倒计时：{{ timeLeft }}</span>
    </header>
    <main class="gym-main">
      <!-- 点击检测覆盖层 - 确保最高优先级接收点击 -->
      <div class="click-catcher" @click="handleClickCatcher($event)"></div>
      
      <!-- SVG容器 -->
      <svg class="mole-svg-container" 
           :width="svgWidth" 
           :height="svgHeight"
           ref="moleSvg">
        <!-- 地鼠元素 -->
        <g v-for="mole in showMoles" :key="mole.id">
          <g :transform="`translate(${mole.x}, ${mole.y})`">
            <!-- 大幅扩大的点击热区（视觉大小的2倍） -->
            <rect 
              x="-84" 
              y="-84" 
              width="252" 
              height="252" 
              fill="transparent"
              :data-mole-id="mole.id"
            />
            <!-- 地鼠图像 -->
            <image
              :width="moleWidth"
              :height="moleHeight"
              :href="moleImage"
              :class="{ 'mole-hit': mole.isHit }"
              :data-mole-id="mole.id"
              preserveAspectRatio="xMidYMid meet"
            />
            
            <!-- 调试用：显示实际点击区域（可选） -->
            <rect 
              v-if="debugMode"
              x="-84" 
              y="-84" 
              width="252" 
              height="252" 
              fill="transparent"
              stroke="rgba(0,255,255,0.3)"
              stroke-width="2"
            />
          </g>
        </g>
        
        <!-- 得分动画 -->
        <g v-for="(scoreAnim, index) in scoreAnimations" :key="index">
          <text
            :x="scoreAnim.x"
            :y="scoreAnim.y"
            :fill="scoreAnim.color"
            :opacity="scoreAnim.opacity"
            :font-size="scoreAnim.fontSize"
            :font-weight="700"
            text-anchor="middle"
            dominant-baseline="middle"
          >
            +{{ scorePerHit }}
          </text>
        </g>
        
        <!-- 点击反馈动画 -->
        <g v-for="(feedback, index) in clickFeedback" :key="index">
          <circle
            :cx="feedback.x"
            :cy="feedback.y"
            :r="feedback.radius"
            :fill="feedback.color"
            :opacity="feedback.opacity"
          />
        </g>

        <!-- 调试用：显示点击位置 -->
        <circle 
          v-if="debugClickPos.x !== null && debugMode" 
          :cx="debugClickPos.x" 
          :cy="debugClickPos.y" 
          r="10" 
          fill="rgba(255,0,0,0.7)" 
        />
      </svg>
      
      <!-- 游戏开始倒计时 -->
      <transition name="countdown-fade">
        <div v-if="showCountdown" class="countdown-overlay">
          <span class="countdown-num">{{ countdownNum }}</span>
        </div>
      </transition>
      
      <!-- 得分面板 -->
      <transition name="score-panel">
        <div v-if="gameOver" class="score-panel-top-wrap">
          <div class="score-panel">
            <img src="/img/tcdefen.png" class="score-bg" alt="得分面板" />
            <div class="score-text">
              <div class="score-title">你最终成绩为</div>
              <div class="score-value">{{ score }}</div>
            </div>
          </div>
        </div>
      </transition>
    </main>
    
    <!-- 游戏结束 -->
    <transition name="avatar-fade">
      <div v-if="gameOver" class="avatar-tip-img-wrap">
        <div class="avatar-tip">辛苦了，吃碗面加油。</div>
        <button class="honor-btn" @click="onHonorClick">查看荣誉</button>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

// 配置参数
let projectName = ref('艺术体操')
let scorePerHit = ref(10)
let countdownTime = ref(30)
let randomCount = ref(3) // 每波固定3个

// 调试模式 - 帮助定位问题
let debugMode = ref(false) // 可在浏览器控制台设置为true: app.config.globalProperties.debugMode = true

// SVG相关配置
const moleImage = ref('/img/tcpicha.png')
const moleWidth = ref(84) // 地鼠宽度（px）
const moleHeight = ref(84) // 地鼠高度（px）
const svgWidth = ref(0)
const svgHeight = ref(0)
const moleSvg = ref(null)

// 游戏状态
let score = ref(0)
let timeLeft = ref(countdownTime.value)
let showMoles = ref([]) // {x, y, id, velocityX, velocityY, spawnTime, isHit}
let gameOver = ref(false)
let showCountdown = ref(true)
let countdownNum = ref(3)
let scoreAnimations = ref([]) // 得分动画
let clickFeedback = ref([]) // 点击反馈动画
let debugClickPos = ref({ x: null, y: null }) // 调试点击位置

// 计时器
let gameTimer = null
let animationInterval = null
let moleId = 0
let countdownTimer = null
let currentWaveId = 0

// 动画参数
const ANIMATION_FPS = 60
const ANIMATION_INTERVAL = 1000 / ANIMATION_FPS
let LIFETIME = 3500
let GRAVITY = 0.22
let BOUNCE_FACTOR = 0.78
let MIN_SPEED = 3.0
let MAX_SPEED = 5.0
let GROUND_Y = 0 // 地面位置（底部）
let EDGE_WIDTH_RATIO = 0.15

let token = localStorage.getItem('jwt_token') || ''
let router = useRouter()
const instance = getCurrentInstance()

// 暴露调试模式到全局，方便在控制台切换
onMounted(() => {
  if (instance) {
    instance.appContext.config.globalProperties.$setDebugMode = (mode) => {
      debugMode.value = mode
      console.log(`调试模式已${mode ? '开启' : '关闭'}`)
    }
    console.log('可在控制台输入: $setDebugMode(true) 开启调试模式')
  }
})

// 获取游戏配置
async function fetchGameSettings() {
  try {
    let res = await fetch('http://localhost:8081/api/game-settings/gymnastics/active', {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    let data = await res.json()
    let config = Array.isArray(data) ? data[data.length - 1] : data
    if (config && config.isActive !== false) {
      projectName.value = config.projectName || projectName.value
      scorePerHit.value = config.score || scorePerHit.value
      countdownTime.value = config.countdownTime || countdownTime.value
      timeLeft.value = countdownTime.value
    }
  } catch (e) {
    console.error('获取配置失败', e)
  }
}

// 生成新一波地鼠
function spawnNewWave() {
  if (gameOver.value) return
  
  currentWaveId++
  let newMoles = []
  let leftAreaEnd = svgWidth.value * EDGE_WIDTH_RATIO
  let rightAreaStart = svgWidth.value * (1 - EDGE_WIDTH_RATIO)
  let maxLeftX = leftAreaEnd - moleWidth.value / 2
  let minRightX = rightAreaStart - moleWidth.value / 2
  
  let heightRange = svgHeight.value * 0.15
  let minY = svgHeight.value * 0.15
  let maxY = minY + heightRange
  
  for (let i = 0; i < randomCount.value; i++) {
    let validMole = null
    let tries = 0
    const maxTries = 200
    
    while (!validMole && tries < maxTries) {
      tries++
      const isLeft = Math.random() > 0.5
      
      let startX
      if (isLeft) {
        startX = (moleWidth.value / 4) + Math.random() * (maxLeftX - moleWidth.value / 4)
      } else {
        startX = minRightX + Math.random() * (svgWidth.value - minRightX - moleWidth.value / 4)
      }
      
      const startY = minY + Math.random() * heightRange
      const speed = MIN_SPEED + Math.random() * (MAX_SPEED - MIN_SPEED)
      const velocityX = isLeft ? speed : -speed
      
      const tempMole = {
        id: moleId,
        x: startX,
        y: startY,
        velocityX,
        velocityY: 0,
        spawnTime: Date.now(),
        isHit: false
      }
      
      const isTooClose = newMoles.some(m => {
        return Math.hypot(tempMole.x - m.x, tempMole.y - m.y) < 150
      })
      
      if (!isTooClose) {
        validMole = tempMole
      }
    }
    
    if (validMole) {
      newMoles.push(validMole)
    } else {
      const isLeft = i % 2 === 0
      const startX = isLeft ? 
        moleWidth.value / 2 + (i * 60) : 
        svgWidth.value - moleWidth.value - (i * 60)
      
      newMoles.push({
        id: moleId,
        x: startX,
        y: minY + heightRange / 2,
        velocityX: isLeft ? MIN_SPEED : -MIN_SPEED,
        velocityY: 0,
        spawnTime: Date.now(),
        isHit: false
      })
    }
    moleId++
  }
  
  showMoles.value = [...newMoles]
  if (debugMode.value) {
    console.log(`生成新波地鼠: ${newMoles.map(m => m.id).join(',')}`)
  }
}

// 更新地鼠位置
function updateMoles() {
  if (gameOver.value) return
  
  const now = Date.now()
  const updatedMoles = showMoles.value.filter(mole => {
    if (now - mole.spawnTime > LIFETIME) {
      if (debugMode.value) console.log(`地鼠${mole.id}生命周期结束`)
      return false
    }
    if (mole.isHit) {
      if (debugMode.value) console.log(`地鼠${mole.id}已被击中`)
      return false
    }
    
    const speedFactor = ANIMATION_INTERVAL / 16.67
    mole.x += mole.velocityX * speedFactor
    mole.velocityY += GRAVITY * speedFactor
    mole.y += mole.velocityY * speedFactor
    
    // 地面碰撞
    if (mole.y + moleHeight.value > GROUND_Y) {
      const overshoot = (mole.y + moleHeight.value) - GROUND_Y
      mole.y -= overshoot
      mole.velocityY = -mole.velocityY * BOUNCE_FACTOR
      mole.velocityX *= 0.93
    }
    
    return true
  })
  
  showMoles.value = [...updatedMoles]
  
  if (updatedMoles.length === 0) {
    spawnNewWave()
  }
  
  updateScoreAnimations()
  updateClickFeedback()
}

// 更新得分动画
function updateScoreAnimations() {
  if (scoreAnimations.value.length === 0) return
  
  const updatedAnims = scoreAnimations.value.filter(anim => {
    anim.y -= 1.5
    anim.opacity -= 0.02
    anim.fontSize = Math.max(16, anim.fontSize - 0.2)
    return anim.opacity > 0
  })
  
  scoreAnimations.value = updatedAnims
}

// 更新点击反馈
function updateClickFeedback() {
  if (clickFeedback.value.length === 0) return
  
  const updatedFeedback = clickFeedback.value.filter(fb => {
    fb.radius += 0.8
    fb.opacity -= 0.05
    return fb.opacity > 0
  })
  
  clickFeedback.value = updatedFeedback
}

// 添加点击反馈
function addClickFeedback(x, y, isSuccess = true) {
  clickFeedback.value.push({
    x,
    y,
    radius: 5,
    opacity: 1,
    color: isSuccess ? 'rgba(46, 204, 113, 0.6)' : 'rgba(231, 76, 60, 0.6)'
  })
}

// 核心优化：超高精度点击检测
function isPointInMole(mole, clickX, clickY) {
  // 计算地鼠在SVG中的实际位置
  const actualX = mole.x
  const actualY = mole.y
  
  // 大幅扩大点击区域（视觉大小的3倍）
  const padding = 84 // 等于地鼠宽度，扩大100%
  
  // 计算实际点击范围
  const hitArea = {
    left: actualX - padding,
    right: actualX + moleWidth.value + padding,
    top: actualY - padding,
    bottom: actualY + moleHeight.value + padding
  }
  
  // 调试信息
  if (debugMode.value) {
    console.log(`地鼠${mole.id}检测范围: L=${hitArea.left.toFixed(0)}, R=${hitArea.right.toFixed(0)}, T=${hitArea.top.toFixed(0)}, B=${hitArea.bottom.toFixed(0)}`)
    console.log(`点击位置: X=${clickX.toFixed(0)}, Y=${clickY.toFixed(0)}`)
  }
  
  // 判断点击是否在范围内
  return clickX >= hitArea.left &&
         clickX <= hitArea.right &&
         clickY >= hitArea.top &&
         clickY <= hitArea.bottom
}

// 转换屏幕坐标到SVG坐标（核心优化）
function convertScreenToSvg(screenX, screenY) {
  if (!moleSvg.value) return null
  
  // 获取SVG元素的边界框
  const svgRect = moleSvg.value.getBoundingClientRect()
  
  // 计算相对坐标（考虑SVG可能的缩放和位置）
  const point = {
    x: screenX - svgRect.left,
    y: screenY - svgRect.top
  }
  
  // 计算缩放因子（解决SVG缩放导致的坐标偏差）
  const scaleX = svgRect.width / svgWidth.value
  const scaleY = svgRect.height / svgHeight.value
  
  // 应用缩放校正
  if (scaleX && scaleY) {
    point.x /= scaleX
    point.y /= scaleY
  }
  
  return point
}

// 顶层点击捕获器（确保不会错过任何点击）
function handleClickCatcher(event) {
  if (gameOver.value) return
  
  // 阻止事件冒泡和默认行为
  event.stopPropagation()
  event.preventDefault()
  
  // 获取屏幕坐标
  const screenX = event.clientX
  const screenY = event.clientY
  
  // 转换为SVG坐标
  const svgPoint = convertScreenToSvg(screenX, screenY)
  if (!svgPoint) {
    console.error('无法转换坐标')
    return
  }
  
  const clickX = svgPoint.x
  const clickY = svgPoint.y
  
  // 记录调试位置
  debugClickPos.value = { x: clickX, y: clickY }
  setTimeout(() => { debugClickPos.value = { x: null, y: null } }, 500)
  
  if (debugMode.value) {
    console.log(`点击捕获 - 屏幕坐标: (${screenX}, ${screenY})，SVG坐标: (${clickX.toFixed(0)}, ${clickY.toFixed(0)})`)
  }
  
  // 获取当前活跃地鼠列表（复制一份避免检测中被修改）
  const activeMoles = [...showMoles.value].filter(m => !m.isHit)
  
  if (debugMode.value) {
    console.log(`当前活跃地鼠: ${activeMoles.map(m => m.id).join(',')}`)
  }
  
  // 按距离排序，优先检测最近的地鼠
  activeMoles.sort((a, b) => {
    const distA = Math.hypot(a.x - clickX, a.y - clickY)
    const distB = Math.hypot(b.x - clickX, b.y - clickY)
    return distA - distB
  })
  
  // 检测点击
  let hitMoleId = null
  for (const mole of activeMoles) {
    if (isPointInMole(mole, clickX, clickY)) {
      hitMoleId = mole.id
      break
    }
  }
  
  // 处理结果
  if (hitMoleId !== null) {
    hitMole(hitMoleId, clickX, clickY)
    addClickFeedback(clickX, clickY, true)
    if (debugMode.value) console.log(`成功命中地鼠: ${hitMoleId}`)
  } else {
    addClickFeedback(clickX, clickY, false)
    if (debugMode.value) console.log('未命中任何地鼠')
  }
}

// 点击地鼠处理（确保状态立即更新）
function hitMole(id, clickX, clickY) {
  // 立即更新地鼠状态（使用新数组确保响应式）
  const updatedMoles = showMoles.value.map(mole => 
    mole.id === id ? { ...mole, isHit: true } : mole
  )
  showMoles.value = updatedMoles
  
  // 增加分数
  score.value += scorePerHit.value
  
  // 添加得分动画
  const mole = updatedMoles.find(m => m.id === id)
  if (mole) {
    scoreAnimations.value.push({
      x: mole.x + moleWidth.value / 2,
      y: mole.y,
      color: '#e74c3c',
      opacity: 1,
      fontSize: 24
    })
  }
  
  // 分数闪烁效果
  const scoreElement = document.querySelector('.score-display')
  if (scoreElement) {
    scoreElement.classList.add('score-flash')
    setTimeout(() => scoreElement.classList.remove('score-flash'), 300)
  }
}

// 开始倒计时
function startCountdown() {
  showCountdown.value = true
  countdownNum.value = 3
  countdownTimer = setInterval(() => {
    if (countdownNum.value > 1) {
      countdownNum.value--
    } else {
      clearInterval(countdownTimer)
      showCountdown.value = false
      startGame()
    }
  }, 1000)
}

// 开始游戏
function startGame() {
  timeLeft.value = countdownTime.value
  score.value = 0
  gameOver.value = false
  showMoles.value = []
  moleId = 0
  
  spawnNewWave()
  animationInterval = setInterval(updateMoles, ANIMATION_INTERVAL)
  
  gameTimer = setInterval(() => {
    timeLeft.value--
    if (timeLeft.value <= 0) {
      endGame()
    }
  }, 1000)
}

// 结束游戏
function endGame() {
  gameOver.value = true
  showMoles.value = []
  clearInterval(gameTimer)
  clearInterval(animationInterval)
}

function onHonorClick() {
  router.push('/gym-share')
}

// 初始化SVG尺寸
function initSvgDimensions() {
  const mainElement = document.querySelector('.gym-main')
  if (mainElement && moleSvg.value) {
    svgWidth.value = mainElement.offsetWidth
    svgHeight.value = mainElement.offsetHeight
    GROUND_Y = svgHeight.value // 地鼠落到屏幕底部
    if (debugMode.value) {
      console.log(`SVG尺寸初始化: ${svgWidth.value}x${svgHeight.value}, 地面位置: ${GROUND_Y}`)
    }
  }
}

// 处理窗口大小变化
function handleResize() {
  initSvgDimensions()
  if (!gameOver.value && showMoles.value.length > 0) {
    spawnNewWave()
  }
}

onMounted(() => {
  initSvgDimensions()
  window.addEventListener('resize', handleResize)
  fetchGameSettings()
  
  nextTick(() => {
    startCountdown()
  })
})

onUnmounted(() => {
  clearInterval(gameTimer)
  clearInterval(countdownTimer)
  clearInterval(animationInterval)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.gym-container {
  width: 100vw;
  height: 100vh;
  background: url('/img/tcbj.png') center center / cover no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;
  position: relative;
}

.gym-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: rgb(255,240,219);
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 700;
  color: #222;
  z-index: 99;
  padding-top: env(safe-area-inset-top);
}

.score-display {
  transition: all 0.3s;
}
.score-flash {
  color: #e74c3c;
  transform: scale(1.2);
}

.gym-main {
  flex: 1;
  width: 100%;
  position: relative;
  overflow: hidden;
  margin-top: 80px;
}

/* 关键优化：顶层点击捕获器，确保不会错过任何点击 */
.click-catcher {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 101; /* 最高层级，确保首先接收点击 */
  pointer-events: auto; /* 确保能接收点击事件 */
}

.mole-svg-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 100;
}

.mole-svg-container image {
  transition: all 0.15s ease-out;
  transform-origin: center;
}

.mole-hit {
  opacity: 0;
  transform: scale(0.5);
}

.countdown-overlay {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-num {
  font-size: 6rem;
  color: #e74c3c;
  font-weight: bold;
  text-shadow: 0 0.25rem 1.5rem rgba(231,76,60,0.18);
  animation: countdown-scale 0.8s cubic-bezier(.4,1.2,.6,1);
}

@keyframes countdown-scale {
  0% { transform: scale(0.2); opacity: 0; }
  60% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

.score-panel-top-wrap {
  position: fixed;
  left: 50%;
  top: -80px;
  transform: translateX(-50%);
  width: 340px;
  max-width: 90vw;
  z-index: 100;
}

.score-panel {
  position: relative;
  width: 100%;
  margin-top: 80px;
}

.score-bg {
  width: 100%;
  display: block;
}

.score-text {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(10%);
  text-align: center;
}

.score-title {
  color: #e74c3c;
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.score-value {
  color: #e74c3c;
  font-size: 2.5rem;
  font-weight: bold;
}

.avatar-tip-img-wrap {
  position: fixed;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 120;
}

.avatar-tip {
  font-size: clamp(0.9rem, 3vw, 1.2rem);
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.honor-btn {
  width: min(80vw, 320px);
  height: 2.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #ffb347 0%, #ffcc33 100%);
  border: none;
  border-radius: 1.5rem;
  cursor: pointer;
  margin-top: 2.2rem;
  z-index: 121; /* 确保按钮可点击 */
  position: relative;
}

.honor-btn:active {
  opacity: 0.85;
}

.countdown-fade-enter-active, .countdown-fade-leave-active,
.avatar-fade-enter-active, .avatar-fade-leave-active {
  transition: opacity 0.4s;
}

.countdown-fade-enter-from, .countdown-fade-leave-to,
.avatar-fade-enter-from, .avatar-fade-leave-to {
  opacity: 0;
}

.score-panel-enter-active, .score-panel-leave-active {
  transition: transform 0.7s cubic-bezier(.4,1.2,.6,1), opacity 0.7s;
}

.score-panel-enter-from {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
}

.score-panel-enter-to {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
</style>
