<template>
  <div class="game-container">
    <!-- 游戏背景 -->
    <div class="space-background"></div>
    
    <!-- 星星装饰 -->
    <div class="stars"></div>
    
    <!-- 火箭元素 - 始终从起始位置出发和返回 -->
    <div 
      class="rocket"
      :style="{ bottom: `${rocketPosition}px` }"
    >
      <div class="rocket-body">
        <div class="rocket-window"></div>
        <!-- <div class="rocket-fin"></div> -->
      </div>
      <div class="rocket-flame" v-if="isLaunching"></div>
    </div>
    
    <!-- 发射按钮 -->
    <button 
      class="launch-button"
      @click.prevent="startLaunch"
      v-if="!isLaunching && !isTimeUp && gameStarted"
      :disabled="!canLaunch"
    >
      发射火箭
    </button>
    
    <!-- 重置按钮 -->
    <button 
      class="reset-button"
      @click.prevent="resetGame"
      v-if="isTimeUp"
    >
      再来一次
    </button>
    
    <!-- 状态指示器 -->
    <div class="status-indicator">
      上升高度: {{ Math.round(rocketPosition - startPosition) }}px / {{ maxClimbHeight }}px
      <br>
      游戏剩余时间: {{ Math.round(gameTimeLeft) }}s
      <br>
      当前得分: {{ score }}
    </div>
    
    <!-- 游戏规则弹框 -->
    <div class="modal-overlay" v-if="showRulesModal">
      <div class="modal">
        <h2>游戏规则</h2>
        <ul>
          <li>待填充</li>
          <li>待填充</li>
          <li>待填充</li>
          <li>待填充</li>
          <!-- <li>火箭从底部上方100px处发射，最高可上升1400px</li>
          <li>每次完整往返（准确返回起始位置）得1分</li>
          <li>游戏时长30秒，时间结束后统计总得分</li>
          <li>火箭返回起始位置后可立即再次发射</li> -->
        </ul>
        <button @click="startCountdown">开始游戏</button>
      </div>
    </div>
    
    <!-- 3秒倒计时 -->
    <div class="countdown-overlay" v-if="showCountdown">
      <div class="countdown">{{ countdownValue }}</div>
    </div>
    
    <!-- 游戏结束弹窗 -->
    <div class="modal-overlay" v-if="isTimeUp">
      <div class="modal">
        <h2>时间到!</h2>
        <p>你的最终得分: {{ score }}</p>
        <p>30秒内完成了 {{ score }} 次发射</p>
        <button @click="resetGame">再来一次</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 火箭核心参数 - 确保起始和返回位置一致
const startPosition = 100; // 固定起始位置（底部上方100px）
const rocketPosition = ref(startPosition); // 当前位置
const isLaunching = ref(false); // 是否正在发射中
let animationFrameId = null; // 动画帧ID
const canLaunch = ref(false); // 是否可以发射

// 游戏状态变量
const showRulesModal = ref(true); // 显示规则弹窗
const showCountdown = ref(false); // 显示倒计时
const countdownValue = ref(3); // 倒计时数值
const gameStarted = ref(false); // 游戏是否开始
const gameTimeLeft = ref(30); // 剩余游戏时间(秒)
const isTimeUp = ref(false); // 游戏时间是否结束
const score = ref(0); // 得分

// 配置参数
const pageHeight = 2000; // 页面总高度
const maxHeight = 1500; // 火箭最大底部距离
const maxClimbHeight = maxHeight - startPosition; // 最大上升高度(1400px)
const flightDuration = 3500; // 单次飞行时间(毫秒)
const viewportHeight = ref(0); // 视口高度
const peakViewportPercentage = 0.15; // 最高点在视口的百分比位置(15%)

// 计算视口高度
const calculateViewport = () => {
  viewportHeight.value = window.innerHeight;
};

// 开始倒计时
const startCountdown = () => {
  showRulesModal.value = false;
  showCountdown.value = true;
  countdownValue.value = 3;
  
  const countdownInterval = setInterval(() => {
    countdownValue.value--;
    
    if (countdownValue.value <= 0) {
      clearInterval(countdownInterval);
      showCountdown.value = false;
      startGameTimer();
    }
  }, 1000);
};

// 开始游戏计时
const startGameTimer = () => {
  gameStarted.value = true;
  canLaunch.value = true; // 允许首次发射
  
  const gameInterval = setInterval(() => {
    gameTimeLeft.value--;
    
    if (gameTimeLeft.value <= 0) {
      clearInterval(gameInterval);
      endGame();
    }
  }, 1000);
};

// 结束游戏
const endGame = () => {
  isTimeUp.value = true;
  gameStarted.value = false;
  canLaunch.value = false;
};

// 开始发射火箭
const startLaunch = () => {
  if (isLaunching.value || !canLaunch.value || isTimeUp.value) return;
  
  isLaunching.value = true;
  canLaunch.value = false; // 发射过程中禁用按钮
  const startTime = Date.now();
  
  animateRocket(startTime);
};

// 火箭动画逻辑 - 确保精确返回起始位置
const animateRocket = (startTime) => {
  if (!isLaunching.value || isTimeUp.value) return;
  
  const elapsed = Date.now() - startTime;
  const progress = Math.min(elapsed / flightDuration, 1);
  
  // 计算火箭位置（使用精确的数学曲线确保往返位置一致）
  if (progress <= 0.5) {
    // 上升阶段：从起始位置到最高点
    const ascentProgress = progress / 0.5;
    // 使用正弦曲线确保平滑加速和减速
    rocketPosition.value = startPosition + maxClimbHeight * Math.sin(ascentProgress * Math.PI / 2);
    console.log(rocketPosition.value);
  } else {
    // 下降阶段：从最高点精确返回起始位置
    const descentProgress = (progress - 0.5) / 0.5;
    // 使用余弦曲线确保对称运动，精确回到起点
    rocketPosition.value = startPosition + maxClimbHeight * Math.cos(descentProgress * Math.PI / 2);
  }
  
  // 页面跟随滚动
  scrollWithRocket(progress);
  
  // 飞行结束 - 强制回到精确起始位置
  if (progress >= 1) {
    isLaunching.value = false;
    // 强制设置为起始位置，消除计算误差
    rocketPosition.value = startPosition;
    score.value++; // 完成一次发射，加分
    canLaunch.value = true; // 允许再次发射
    return;
  }
  
  // 继续动画
  animationFrameId = requestAnimationFrame(() => animateRocket(startTime));
};

// 页面跟随滚动逻辑
const scrollWithRocket = (flightProgress) => {
  try {
    // 火箭在页面中的绝对位置（距离顶部）
    const rocketTop = pageHeight - rocketPosition.value - 80; // 80是火箭高度
    
    // 计算目标滚动位置
    let targetScroll;
    
    // 根据飞行进度动态调整火箭在视口中的位置
    if (flightProgress <= 0.5) {
      // 上升阶段：从底部(85%位置)移动到顶部(15%位置)
      const ascentRatio = flightProgress / 0.5;
      const targetViewportPosition = 
        viewportHeight.value * 0.85 + // 起始视口位置
        ascentRatio * (viewportHeight.value * (peakViewportPercentage - 0.85));
      targetScroll = rocketTop - targetViewportPosition;
    } else if (flightProgress <= 0.6) {
      // 最高点附近：保持在15%位置
      targetScroll = rocketTop - (viewportHeight.value * peakViewportPercentage);
    } else {
      // 下降阶段：从15%位置回到85%位置
      const descentRatio = (flightProgress - 0.6) / 0.4;
      const targetViewportPosition = 
        viewportHeight.value * peakViewportPercentage -
        descentRatio * (viewportHeight.value * (peakViewportPercentage - 0.85));
      targetScroll = rocketTop - targetViewportPosition;
    }
    
    // 限制滚动范围
    targetScroll = Math.max(
      0,
      Math.min(
        targetScroll,
        pageHeight - viewportHeight.value
      )
    );
    
    window.scrollTo({
      top: targetScroll,
      behavior: 'auto'
    });
  } catch (error) {
    console.error('滚动错误:', error);
  }
};

// 重置游戏 - 确保回到起始位置
const resetGame = () => {
  // 重置火箭状态到精确起始位置
  rocketPosition.value = startPosition;
  isLaunching.value = false;
  canLaunch.value = false;
  
  // 重置游戏状态
  score.value = 0;
  gameTimeLeft.value = 30;
  isTimeUp.value = false;
  showRulesModal.value = true;
  
  // 清理动画
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  
  // 滚动到初始位置
  window.scrollTo({
    top: pageHeight - viewportHeight.value,
    behavior: 'smooth'
  });
};

// 初始化
onMounted(() => {
  window.addEventListener('resize', calculateViewport);
  calculateViewport();
  
  // 初始滚动到正确位置，显示起始状态的火箭
  setTimeout(() => {
    window.scrollTo({
      top: pageHeight - viewportHeight.value,
      behavior: 'smooth'
    });
  }, 100);
});

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', calculateViewport);
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
});
</script>

<style scoped>
/* 保持原有样式不变 */
html, body {
  overflow: hidden !important;
  touch-action: none !important;
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
}

.game-container {
  position: relative;
  height: 2000px;
  width: 100%;
  overflow: hidden;
}

.space-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("/img/gzaimian-bg1.png") repeat center center;
  /* background: linear-gradient(to top, #050a30 0%, #1a1a2e 50%, #0f3460 100%); */
  z-index: 1;
}

.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.stars::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 40px 70px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 50px 160px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 90px 40px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 130px 80px, #ffffff, rgba(0,0,0,0)),
    radial-gradient(2px 2px at 160px 120px, #ffffff, rgba(0,0,0,0));
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: twinkle 4s linear infinite;
}

@keyframes twinkle {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.rocket {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 80px;
  z-index: 10;
}

.rocket-body {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #ff6b6b, #ee5253);
  border-radius: 15px 15px 5px 5px;
}

.rocket-window {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #48dbfb;
  border-radius: 50%;
  border: 2px solid #c8d6e5;
}

.rocket-fin {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 20px;
  background: #ff6b6b;
  clip-path: polygon(50% 100%, 0 0, 100% 0);
}

.rocket-flame {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 40px;
  background: linear-gradient(to bottom, #ffa502, #ff4757);
  border-radius: 50% 50% 20% 20%;
  animation: flame 0.1s infinite alternate;
}

@keyframes flame {
  0% { height: 40px; width: 20px; }
  100% { height: 50px; width: 25px; }
}

.launch-button, .reset-button {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border: none;
  border-radius: 30px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  z-index: 100;
  transition: all 0.2s;
}

.launch-button {
  background-color: #2ecc71;
  color: white;
}

.launch-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: translateX(-50%) scale(1);
}

.launch-button:not(:disabled):hover {
  background-color: #27ae60;
  transform: translateX(-50%) scale(1.05);
}

.reset-button {
  background-color: #3498db;
  color: white;
}

.reset-button:hover {
  background-color: #2980b9;
  transform: translateX(-50%) scale(1.05);
}

.status-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  padding: 10px 16px;
  background-color: rgba(0,0,0,0.7);
  color: white;
  border-radius: 8px;
  font-size: 16px;
  z-index: 100;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  padding: 24px;
  border-radius: 10px;
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.modal h2 {
  color: #333;
  margin-top: 0;
}

.modal ul {
  text-align: left;
  margin: 20px 0;
  padding-left: 20px;
}

.modal li {
  margin-bottom: 8px;
}

.modal p {
  font-size: 18px;
  margin: 15px 0;
}

.modal button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.modal button:hover {
  background-color: #2980b9;
}

.countdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.countdown {
  color: white;
  font-size: 120px;
  font-weight: bold;
  animation: pulse 1s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.3); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}
</style>
