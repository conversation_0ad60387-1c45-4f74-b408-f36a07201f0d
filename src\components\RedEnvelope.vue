<!-- 红包组件点击显示红包，RedEnvelope.vue -->
<template>
  <div class="envelope-wrapper">
    <div class="envelope" @click="openEnvelope" :class="{ opened: isOpened }">
      <!-- Layer 1: 底层信纸，始终可见：展示金额 -->
      <div class="layer letter">
        <div class="amount">{{ amount }}元</div>
      </div>

      <!-- Layer 2: 红色信封底部（含印章），静态 -->
      <div class="layer body">
        <div class="seal">领</div>
      </div>

      <!-- Layer 3: 红色信封正面（带圆章 Logo 占位），静态 -->
      <div class="layer face">
        <div class="face-logo"></div>
      </div>

      <!-- Layer 4: 红色封盖，点击翻转 -->
      <div class="layer flap">
        <div class="flap-logo"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isOpened = ref(false);
const amount = '88.88';

function openEnvelope() {
  if (!isOpened.value) {
    isOpened.value = true;
  }
}
</script>

<style scoped>
/* 遮罩居中表示 */
.envelope-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}
/* 容器与透视 */
.envelope-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  perspective: 1000px;
}

.envelope {
  position: relative;
  width: 12.5rem;    /* 200px */
  height: 16.25rem;  /* 260px */
  cursor: pointer;
}

/* 图层基础样式 */
.layer {
  position: absolute;
  left: 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Layer 1: 信纸 */
.letter {
  bottom: 0;
  height: 100%;
  background: #fff3c2;
  border-radius: 1rem;
  z-index: 1;
}
.amount {
  font-size: 1.5rem;
  color: #d32f2f;
  font-weight: bold;
}

/* Layer 2: 信封底部（70% 高度） */
.body {
  bottom: 0;
  height: 70%;
  background: linear-gradient(to top, #e53935, #f44336);
  border-radius: 1rem;
  z-index: 2;
}
.seal {
  width: 3.75rem;
  height: 3.75rem;
  background: gold;
  border-radius: 50%;
  font-size: 1.5rem;
  color: #fff;
  text-align: center;
  line-height: 3.75rem;
  box-shadow: 0 0.125rem 0.375rem rgba(0,0,0,0.2);
}

/* Layer 3: 信封正面（40% 高度） */
.face {
  bottom: 30%;
  height: 40%;
  background: linear-gradient(to top, #d32f2f, #e53935);
  border-radius: 1rem;
  z-index: 3;
}
.face-logo {
  width: 3rem;
  height: 3rem;
  background: yellow;
  border-radius: 50%;
}

/* Layer 4: 封盖（顶部翻转） */
.flap {
  top: 0;
  height: 50%;
  background: linear-gradient(to top, #d32f2f, #e53935);
  border-radius: 1rem 1rem 0 0;
  transform-origin: top center;
  backface-visibility: hidden;
  transition: transform 0.6s ease;
  z-index: 4;
}
.flap-logo {
  width: 2.5rem;
  height: 1.25rem;
  background: #2196f3;
}

/* 打开状态：封盖翻转 */
.envelope.opened .flap {
  transform: rotateX(180deg);
}

/* 📱 移动端适配 */
@media (max-width: 480px) {
  .envelope {
    width: 10rem;
    height: 13rem;
  }

  .amount {
    font-size: 1.2rem;
  }

  .seal {
    width: 3rem;
    height: 3rem;
    font-size: 1.2rem;
    line-height: 3rem;
  }

  .face-logo {
    width: 2.5rem;
    height: 2.5rem;
  }

  .flap-logo {
    width: 2rem;
    height: 1rem;
  }
}

</style>
