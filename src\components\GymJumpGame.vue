//蹦床游戏
<template>
  <div class="jump-container">
    <!-- 顶部固定导航栏 -->
    <header class="jump-header jump-header-fixed">
      <span>项目：{{ projectName }}</span>
      <span>分数：{{ score }}</span>
      <span>倒计时：{{ timeLeft }}秒</span>
    </header>
    
    <!-- 游戏主区域 -->
    <main class="jump-main">
      <!-- 游戏元素 -->
      <div class="game-area">
        <!-- 角色 -->
        <div 
          class="character" 
          :style="{ bottom: characterPosition + 'px' }"
          :class="{ 'character-jump': isJumping }"
        >
          <img src="/img/character.png" alt="跳跃角色" class="character-img" />
        </div>
        
        <!-- 蹦床 -->
        <div 
          class="trampoline" 
          :style="{ height: trampolineHeight + 'px' }"
          :class="{ 'trampoline-bounce': isBouncing }"
        >
          <img src="/img/trampoline.png" alt="蹦床" class="trampoline-img" />
        </div>
        
        <!-- 蓄力条 -->
        <div class="power-bar-container">
          <div class="power-bar" :style="{ width: powerPercentage + '%', backgroundColor: powerBarColor }"></div>
        </div>
        
        <!-- 跳跃按钮 -->
        <button 
          class="jump-btn" 
          @touchstart="startCharge" 
          @touchend="releaseCharge"
          @mousedown="startCharge" 
          @mouseup="releaseCharge"
          :disabled="isJumping || gameOver"
        >
          {{ isCharging ? '蓄力中...' : '按住蓄力' }}
        </button>
        
        <!-- 提示文本 -->
        <div class="instructions">按住按钮蓄力，松开跳跃</div>
      </div>
      
      <!-- 游戏开始321倒计时 -->
      <transition name="countdown-fade">
        <div v-if="showCountdown" class="countdown-overlay">
          <span class="countdown-num">{{ countdownNum }}</span>
        </div>
      </transition>
      
      <!-- 游戏结束得分面板 -->
      <transition name="score-panel">
        <div v-if="gameOver" class="score-panel-top-wrap">
          <div class="score-panel">
            <img src="/img/score-bg.png" class="score-bg" alt="得分面板背景" />
            <div class="score-text">
              <div class="score-title">你最终成绩为</div>
              <div class="score-value">{{ score }}</div>
            </div>
          </div>
        </div>
      </transition>
    </main>
    
    <!-- 游戏结束底部图片和按钮 -->
    <transition name="avatar-fade">
      <div v-if="gameOver" class="avatar-tip-img-wrap">
        <div class="avatar-tip">太棒了，继续加油！</div>
        <img src="/img/gameover-avatar.png" class="gameover-avatar-bottom" alt="游戏结束角色" />
        <button class="restart-btn" @click="restartGame">再来一次</button>
        <button class="honor-btn" @click="onHonorClick">查看荣誉</button>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'

// 游戏配置参数
const projectName = ref('跳跃挑战')
const baseScore = ref(10)       // 基础分数
const gameTime = ref(60)        // 游戏时长(秒)
const maxPower = ref(100)       // 最大蓄力值
const gravity = ref(0.7)        // 重力参数

// 游戏状态
const score = ref(0)
const highScore = ref(0)
const timeLeft = ref(gameTime.value)
const isCharging = ref(false)
const isJumping = ref(false)
const isBouncing = ref(false)
const power = ref(0)
const gameOver = ref(false)
const showCountdown = ref(true)
const countdownNum = ref(3)

// 计时器
let chargeInterval = null
let gameTimer = null
let countdownTimer = null

// 蹦床相关
const baseTrampolineHeight = 30
const minTrampolineHeight = 10
const trampolineHeight = ref(baseTrampolineHeight)

// 级别配置
const levels = [
  { height: 100, bgColor: '#FFD166' }, // 黄色
  { height: 200, bgColor: '#06D6A0' }, // 绿色
  { height: 300, bgColor: '#118AB2' }, // 蓝色
  { height: 400, bgColor: '#EF476F' }, // 红色
  { height: 500, bgColor: '#9B5DE5' }  // 紫色
]
const currentLevel = ref(0)
const currentBgColor = computed(() => levels[currentLevel.value].bgColor)

// 计算属性
const powerPercentage = computed(() => {
  return Math.min(100, (power.value / maxPower.value) * 100)
})

const powerBarColor = computed(() => {
  const pct = powerPercentage.value
  if (pct < 30) return '#4CAF50'    // 绿色
  if (pct < 70) return '#FFC107'    // 黄色
  return '#F44336'                  // 红色
})

const characterPosition = computed(() => {
  // 最大位置限制为屏幕高度的80%
  const maxPos = Math.min(600, window.innerHeight * 0.7)
  return Math.min(maxPos, (score.value / 500) * maxPos)
})

// 路由
const router = useRouter()

// 从API获取游戏配置
async function fetchGameSettings() {
  try {
    console.log('请求游戏配置接口')
    // 实际项目中替换为真实接口
    const mockData = {
      projectName: "跳跃挑战",
      baseScore: 10,
      gameTime: 60,
      maxPower: 100,
      gravity: 0.7
    }
    
    // 应用配置
    projectName.value = mockData.projectName
    baseScore.value = mockData.baseScore
    gameTime.value = mockData.gameTime
    maxPower.value = mockData.maxPower
    gravity.value = mockData.gravity
    timeLeft.value = gameTime.value
    
    console.log('游戏配置加载成功')
  } catch (e) {
    console.error('获取游戏配置失败', e)
  }
}

// 开始蓄力
function startCharge() {
  if (isJumping.value || gameOver.value) return
  
  isCharging.value = true
  power.value = 0
  isBouncing.value = false
  
  // 每30ms增加1点蓄力值，同时改变蹦床高度
  chargeInterval = setInterval(() => {
    power.value += 1
    
    // 限制最大蓄力
    if (power.value >= maxPower.value) {
      power.value = maxPower.value
      clearInterval(chargeInterval)
    }
    
    // 计算蹦床高度（蓄力越大，蹦床越矮）
    const heightRatio = 1 - (power.value / maxPower.value)
    trampolineHeight.value = minTrampolineHeight + 
                           (baseTrampolineHeight - minTrampolineHeight) * heightRatio
  }, 30)
}

// 释放蓄力，开始跳跃
function releaseCharge() {
  if (!isCharging.value || gameOver.value) return
  
  clearInterval(chargeInterval)
  isCharging.value = false
  isJumping.value = true
  
  // 触发蹦床弹跳效果
  isBouncing.value = true
  trampolineHeight.value = baseTrampolineHeight
  
  // 计算跳跃得分和初始速度
  const jumpScore = Math.floor((power.value / maxPower.value) * baseScore.value * 10)
  let initialVelocity = power.value * 3
  
  // 跳跃动画
  const jump = () => {
    // 上升阶段增加分数
    score.value += Math.floor(initialVelocity / 5)
    
    // 检查是否升级
    if (currentLevel.value < levels.length - 1 && 
        score.value >= levels[currentLevel.value].height) {
      currentLevel.value++
    }
    
    // 应用重力
    initialVelocity -= gravity.value
    
    // 跳跃结束条件
    if (initialVelocity <= 0) {
      isJumping.value = false
      // 添加基础分
      score.value += jumpScore
      return
    }
    
    requestAnimationFrame(jump)
  }
  
  jump()
}

// 游戏开始倒计时
function startCountdown() {
  showCountdown.value = true
  countdownNum.value = 3
  
  countdownTimer = setInterval(() => {
    if (countdownNum.value > 1) {
      countdownNum.value--
    } else {
      clearInterval(countdownTimer)
      showCountdown.value = false
      startGame()
    }
  }, 1000)
}

// 开始游戏
function startGame() {
  timeLeft.value = gameTime.value
  score.value = 0
  currentLevel.value = 0
  gameOver.value = false
  
  // 游戏计时器
  gameTimer = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--
    } else {
      endGame()
    }
  }, 1000)
}

// 结束游戏
function endGame() {
  gameOver.value = true
  isJumping.value = false
  clearInterval(gameTimer)
  
  // 更新最高分
  if (score.value > highScore.value) {
    highScore.value = score.value
    // 可以在这里保存最高分到本地存储或服务器
  }
}

// 重新开始游戏
function restartGame() {
  gameOver.value = false
  startCountdown()
}

// 查看荣誉
function onHonorClick() {
  router.push('/honors')
}

// 监听游戏状态
watch(isJumping, (newVal) => {
  if (!newVal) {
    isBouncing.value = false
  }
})

// 生命周期钩子
onMounted(async () => {
  await fetchGameSettings()
  startCountdown()
})

onUnmounted(() => {
  clearInterval(chargeInterval)
  clearInterval(gameTimer)
  clearInterval(countdownTimer)
})
</script>

<style scoped>
.jump-container {
  width: 100vw;
  height: 100vh;
  background: url('/img/game-bg.png') center center / cover no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  overflow: hidden;
  position: relative;
}

/* 顶部导航栏 */
.jump-header {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 700;
  color: #222;
  padding: 0 1rem;
}

.jump-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 80px;
  max-width: 100vw;
  z-index: 9999;
  background: rgba(255,240,219,0.9);
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  padding-top: env(safe-area-inset-top);
  margin-top: 0;
}

/* 主游戏区域 */
.jump-main {
  flex: 1;
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 0 0 2rem 0;
  margin-top: 5rem;
}

.game-area {
  width: 100%;
  height: 100%;
  position: relative;
  transition: background-color 0.5s ease;
}

/* 角色样式 */
.character {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  z-index: 4;
  transition: bottom 0.1s ease-out;
}

.character-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.character-jump {
  transition: bottom 0.05s ease-out;
}

/* 蹦床样式 */
.trampoline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 30px;
  z-index: 5;
  transition: height 0.1s ease, transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.trampoline-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.trampoline-bounce {
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0% { height: 10px; transform: translateX(-50%) scaleX(1.2); }
  50% { height: 35px; transform: translateX(-50%) scaleX(0.9); }
  100% { height: 30px; transform: translateX(-50%) scaleX(1); }
}

/* 蓄力条 */
.power-bar-container {
  position: absolute;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 12px;
  background-color: rgba(255,255,255,0.3);
  border-radius: 6px;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) inset;
}

.power-bar {
  height: 100%;
  width: 0%;
  background-color: #4CAF50;
  border-radius: 6px;
  transition: width 0.1s linear;
}

/* 跳跃按钮 */
.jump-btn {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  padding: 18px 40px;
  font-size: 18px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.15s ease;
  z-index: 10;
  width: 80%;
  max-width: 300px;
}

.jump-btn:active {
  background-color: #3e8e41;
  transform: translateX(-50%) scale(0.97);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.jump-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: translateX(-50%);
  box-shadow: none;
}

/* 提示文本 */
.instructions {
  position: absolute;
  top: 100px;
  left: 0;
  right: 0;
  text-align: center;
  color: #333;
  font-size: 14px;
  z-index: 10;
  text-shadow: 0 1px 2px rgba(255,255,255,0.5);
}

/* 321倒计时动画样式 */
.countdown-overlay {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-num {
  font-size: 6rem;
  color: #e74c3c;
  font-weight: bold;
  text-shadow: 0 0.25rem 1.5rem rgba(231,76,60,0.18);
  animation: countdown-scale 0.8s cubic-bezier(.4,1.2,.6,1);
}

@keyframes countdown-scale {
  0% { transform: scale(0.2); opacity: 0; }
  60% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

.countdown-fade-enter-active, .countdown-fade-leave-active {
  transition: opacity 0.4s;
}

.countdown-fade-enter-from, .countdown-fade-leave-to {
  opacity: 0;
}

/* 得分面板 */
.score-panel-top-wrap {
  position: fixed;
  left: 50%;
  top: -80px;
  transform: translateX(-50%);
  width: 340px;
  max-width: 90vw;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-panel {
  position: relative;
  width: 100%;
  margin-top: 80px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-bg {
  width: 100%;
  display: block;
}

.score-text {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transform: translateY(10%);
  pointer-events: none;
}

.score-title {
  color: #e74c3c;
  font-size: 1.3rem;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 0.5rem;
  text-align: center;
}

.score-value {
  color: #e74c3c;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-align: center;
}

.score-panel-enter-active, .score-panel-leave-active {
  transition: transform 0.7s cubic-bezier(.4,1.2,.6,1), opacity 0.7s;
}

.score-panel-enter-from {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
}

.score-panel-enter-to {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

/* 游戏结束底部图片和按钮 */
.avatar-tip-img-wrap {
  position: fixed;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 120;
}

.avatar-tip {
  font-size: clamp(0.9rem, 3vw, 1.2rem);
  color: #e74c3c;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  text-align: center;
  text-shadow: 0 2px 12px rgba(0,0,0,0.08);
  z-index: 121;
}

.gameover-avatar-bottom {
  width: min(60vw, 60vh, 400px);
  height: min(60vw, 60vh, 400px);
  min-width: 120px;
  min-height: 120px;
  max-width: 90vw;
  max-height: 70vh;
  object-fit: contain;
  z-index: 120;
  pointer-events: none;
  animation: avatar-pop 0.7s cubic-bezier(.4,1.2,.6,1);
}

@keyframes avatar-pop {
  0% { transform: scale(0.5); opacity: 0; }
  60% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

.avatar-fade-enter-active, .avatar-fade-leave-active {
  transition: opacity 0.5s;
}

.avatar-fade-enter-from, .avatar-fade-leave-to {
  opacity: 0;
}

/* 按钮样式 */
.restart-btn, .honor-btn {
  margin-top: 1rem;
  width: min(80vw, 320px);
  height: 2.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  border: none;
  border-radius: 1.5rem;
  box-shadow: 0 2px 12px rgba(0,0,0,0.10);
  cursor: pointer;
  transition: all 0.2s;
}

.restart-btn {
  background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
}

.honor-btn {
  background: linear-gradient(90deg, #ffb347 0%, #ffcc33 100%);
}

.restart-btn:active, .honor-btn:active {
  opacity: 0.85;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .jump-header {
    font-size: 1rem;
  }
  
  .jump-btn {
    padding: 15px 30px;
    font-size: 16px;
  }
}
</style>
