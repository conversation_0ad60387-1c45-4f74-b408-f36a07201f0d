<template>
  <div class="share-container">
    <div class="share-title">艺术体操分享</div>
    <img class="share-img" src="/img/tcchimiandf.png" alt="艺术体操" />
    <div class="share-btn-group">
      <button class="share-btn" @click="onReplay">再玩一次</button>
      <button class="share-btn share-btn-red" @click="onShare">分享领红包</button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
function onReplay() {
  router.push('/gym')
}
function onShare() {
  // 可集成分享逻辑
  alert('分享功能开发中...')
}
</script>

<style scoped>
.share-container {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(180deg, #fffbe6 0%, #fff 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0 1.5rem;
  box-sizing: border-box;
}
.share-title {
  font-size: 2rem;
  font-weight: bold;
  color: #e74c3c;
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}
.share-img {
  width: min(60vw, 320px);
  height: min(60vw, 320px);
  max-width: 90vw;
  max-height: 40vh;
  object-fit: contain;
  margin-bottom: 2.5rem;
  border-radius: 1.2rem;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
}
.share-btn-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  align-items: center;
  margin-top: 1.5rem;
}
.share-btn {
  width: min(80vw, 320px);
  height: 2.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #41b883 0%, #6dd5ed 100%);
  border: none;
  border-radius: 1.5rem;
  box-shadow: 0 2px 12px rgba(65,184,131,0.10);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  display: block;
}
.share-btn:active {
  opacity: 0.85;
}
.share-btn-red {
  background: linear-gradient(90deg, #ff5e62 0%, #ff9966 100%);
}
</style> 