<!--余额-->
<script setup lang="ts">
import {ref,onMounted} from "vue";
// @ts-ignore
import {transfer} from "@/http/balanceApi"
// @ts-ignore
import {config,wxPayHandle} from "@/utils/WxUtils"

import { Toast } from "tdesign-mobile-vue";
declare global {
  interface Window {
    WeixinJSBridge: any;
  }
  interface Window {
    wx: any;
  }
}
const WeixinJSBridge = window.WeixinJSBridge;
const wx = window.wx;
const loading = ref(false);
onMounted(async ()=>{
  console.log("页面加载成功...")
  await config();
})
// 余额提现
const balanceHandle = async ()=>{
  loading.value = true
  console.log("点击了提现按钮")
  let res = await transfer()
  loading.value = false
  console.log("res---",res)
  if (res.code !== 200){
    Toast("请求失败,请稍后再试")
    return
  }
  console.log("res----",res.data)
  // console.log("wx---",wx)
  // console.log("WeixinJSBridge---",WeixinJSBridge)

  let data = res.data
  // 微信支付回调
  wxPayHandle(data,function () {
    // 支付成功
    Toast("支付成功")
  })
  console.log("wx.ready--end")
}
</script>

<template>
<div class="balance">

  <t-loading :loading="loading" text="加载中..." fullscreen />
<div class="title" @click="balanceHandle()">余额提现</div>
</div>
</template>

<style scoped lang="scss">
.balance{
  width: 100%;
  height: 100%;
  .title{
    font-size: 30px;
    text-align: center;
    margin-top: 20px;
  }
}
</style>