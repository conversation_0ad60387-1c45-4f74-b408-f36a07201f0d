import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  { path: '/', component: () => import('@/views/Loading.vue') },
  { path: '/home', component: () => import('@/views/home/<USER>') },
  { path: '/test', component: () => import('@/views/game/test.vue') },
  { path: '/pick', component: () => import('@/views/Pick.vue') },
  { path: '/gym', component: () => import('@/views/game/RhythmicGymnastics.vue') },
  {
    path: '/gym-share',
    name: 'ArtGymShare',
    component: () => import('@/views/game/ArtGymShare.vue')
  },
  { path: '/trampoline', component: () => import('@/views/game/trampoline.vue') },
  { path: '/archery', component: () => import('@/views/game/archery.vue') },
  { path: '/balance', component: () => import('@/views/Balance.vue') },
  { path: '/share', component: () => import('@/views/Share.vue') },
]
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router