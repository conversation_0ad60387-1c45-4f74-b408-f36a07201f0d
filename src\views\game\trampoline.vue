<template>
  <div class="game-container"
    @mousedown="startCharging"
    @mouseup="stopCharging"
    @mouseleave="stopCharging"
    @touchstart="startCharging"
    @touchend="stopCharging">

    <!-- 游戏背景 -->
    <div class="space-background"></div>

    <!-- 得分信息显示 -->
    <div class="score-info"  v-show="!showRulesModal" >
      <div class="info-box">
        <div class="score">
          <div>{{ score }}</div><span>分</span>
        </div>
        <div class="time">{{ gameTimeLeft }}s</div>
      </div>
    </div>
    
    <!-- 人物 -->
    <img src="@/assets/img/pick/mgu.png" class="character" :style="{ bottom: `${rocketPosition}px` }" v-show="!showRulesModal" />
    
    <!-- 发射按钮 -->
    <!-- <button 
      class="launch-button"
      @click.prevent="startLaunch"
      v-if="!isLaunching && !isTimeUp && gameStarted"
      :disabled="!canLaunch"
    >
      发射火箭
    </button> -->
    
    <!-- 重置按钮 -->
    <!-- <button 
      class="reset-button"
      @click.prevent="resetGame"
      v-if="isTimeUp"
    >
      再来一次
    </button> -->
    
    <!-- 状态指示器 -->
    <!-- <div class="status-indicator">
      上升高度: {{ Math.round(rocketPosition - startPosition) }}px / {{ maxClimbHeight }}px
      <br>
      游戏剩余时间: {{ Math.round(gameTimeLeft) }}s
      <br>
      当前得分: {{ score }}
    </div> -->
    
    <!-- 游戏规则弹框 -->
    <div class="rules-modal" v-if="showRulesModal">
      <img src="@/assets/img/game/archery/5.png" class="img1" style="margin-bottom: 15px;" />
      <img src="@/assets/img/game/archery/1.png" class="img1" />
      <img src="@/assets/img/game/archery/2.png" class="img1" />
      <img src="@/assets/img/game/archery/3.png" class="img1" />
      <img src="@/assets/img/game/archery/4.png" class="img1" />
      <img src="@/assets/img/game/archery/close.png" class="img2" @click="startCountdown" />
    </div>
    
    <!-- 3秒倒计时 -->
    <div v-if="showCountdown" class="countdown">
      <img src="@/assets/img/game/go.png" class="img1" v-if="countdownValue==0" />
      <img src="@/assets/img/game/one.png" class="img2" v-if="countdownValue==1" />
      <img src="@/assets/img/game/two.png" class="img2" v-if="countdownValue==2" />
      <img src="@/assets/img/game/three.png" class="img2" v-if="countdownValue==3" />
    </div>
    
    <!-- 游戏结束弹窗 -->
    <div class="modal-overlay" v-if="isTimeUp">
      <div class="modal">
        <h2>时间到!</h2>
        <p>你的最终得分: {{ score }}</p>
        <p>30秒内完成了 {{ score }} 次发射</p>
        <button @click="resetGame">再来一次</button>
      </div>
    </div>
  </div>

  <ChargeBar :progress="progress" v-show="!showRulesModal" />
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ChargeBar from '@/components/ChargeBar.vue';

// 人物核心参数
const startPosition = 100; // 起始位置（底部上方100px）
const rocketPosition = ref(startPosition); // 当前位置
const isLaunching = ref(false); // 是否正在发射中
let animationFrameId = null; // 动画帧ID
const canLaunch = ref(false); // 是否可以发射

// 游戏状态变量
const showRulesModal = ref(true); // 显示规则弹窗
const showCountdown = ref(false); // 显示倒计时
const countdownValue = ref(3); // 倒计时数值
const gameStarted = ref(false); // 游戏是否开始
const gameTimeLeft = ref(30); // 剩余游戏时间(秒)
const isTimeUp = ref(false); // 游戏时间是否结束
const score = ref(0); // 得分

// 配置参数
const pageHeight = 2000; // 页面总高度
const maxHeight = 1500; // 人物最大底部距离
const maxClimbHeight = maxHeight - startPosition; // 最大上升高度(1400px)
const flightDuration = 3500; // 单次飞行时间(毫秒)
const viewportHeight = ref(0); // 视口高度
const peakViewportPercentage = 0.15; // 最高点在视口的百分比位置(15%)

// 计算视口高度
const calculateViewport = () => {
  viewportHeight.value = window.innerHeight;
};

// 滚动到页面最底部
const scrollToBottom = () => {
  const bottomPosition = pageHeight - viewportHeight.value;
  window.scrollTo({
    top: bottomPosition,
    behavior: 'smooth' // 平滑滚动到底部
  });
};

// 开始倒计时
const startCountdown = () => {
  showRulesModal.value = false;
  showCountdown.value = true;
  countdownValue.value = 3;
  
  const countdownInterval = setInterval(() => {
    countdownValue.value--;
    
    if (countdownValue.value < 0) {
      clearInterval(countdownInterval);
      showCountdown.value = false;
      startGameTimer();
    }
  }, 1000);
};

// 开始游戏计时
const startGameTimer = () => {
  gameStarted.value = true;
  canLaunch.value = true; // 允许首次发射
  
  const gameInterval = setInterval(() => {
    gameTimeLeft.value--;
    
    if (gameTimeLeft.value <= 0) {
      clearInterval(gameInterval);
      endGame();
    }
  }, 1000);
};

// 结束游戏
const endGame = () => {
  isTimeUp.value = true;
  gameStarted.value = false;
  canLaunch.value = false;
};

// 开始发射人物
const startLaunch = () => {
  if (isLaunching.value || !canLaunch.value || isTimeUp.value) return;
  
  isLaunching.value = true;
  canLaunch.value = false; // 发射过程中禁用按钮
  const startTime = Date.now();
  
  animateRocket(startTime);
};

// 人物动画逻辑 - 核心调整：返回时滚动到底部
const animateRocket = (startTime) => {
  if (!isLaunching.value || isTimeUp.value) return;
  
  const elapsed = Date.now() - startTime;
  const progress = Math.min(elapsed / flightDuration, 1);
  
  // 计算人物位置
  if (progress <= 0.5) {
    // 上升阶段
    const ascentProgress = progress / 0.5;
    rocketPosition.value = startPosition + maxClimbHeight * Math.sin(ascentProgress * Math.PI / 2);
  } else {
    // 下降阶段
    const descentProgress = (progress - 0.5) / 0.5;
    rocketPosition.value = startPosition + maxClimbHeight * Math.cos(descentProgress * Math.PI / 2);
  }
  
  // 页面跟随滚动（上升和下降过程中）
  if (progress < 1) {
    scrollWithRocket(progress);
  }
  
  // 飞行结束 - 回到原点并滚动到底部
  if (progress >= 1) {
    isLaunching.value = false;
    rocketPosition.value = startPosition; // 确保回到精确起始位置
    score.value++; // 加分
    canLaunch.value = true; // 允许再次发射
    
    resetCharging()

    // 关键调整：人物回到原点后，页面滚动到最底部
    scrollToBottom();
    return;
  }
  
  // 继续动画
  animationFrameId = requestAnimationFrame(() => animateRocket(startTime));
};

// 飞行过程中页面跟随滚动
const scrollWithRocket = (flightProgress) => {
  try {
    const rocketTop = pageHeight - rocketPosition.value - 80; // 人物距离页面顶部的距离
    
    let targetScroll;
    
    if (flightProgress <= 0.5) {
      // 上升阶段：从底部移动到15%位置
      const ascentRatio = flightProgress / 0.5;
      const targetViewportPosition = 
        viewportHeight.value * 0.85 + 
        ascentRatio * (viewportHeight.value * (peakViewportPercentage - 0.85));
      targetScroll = rocketTop - targetViewportPosition;
    } else if (flightProgress <= 0.6) {
      // 最高点附近：保持在15%位置
      targetScroll = rocketTop - (viewportHeight.value * peakViewportPercentage);
    } else {
      // 下降阶段：从15%位置向底部移动
      const descentRatio = (flightProgress - 0.6) / 0.4;
      const targetViewportPosition = 
        viewportHeight.value * peakViewportPercentage -
        descentRatio * (viewportHeight.value * (peakViewportPercentage - 0.85));
      targetScroll = rocketTop - targetViewportPosition;
    }
    
    // 限制滚动范围
    targetScroll = Math.max(0, Math.min(targetScroll, pageHeight - viewportHeight.value));
    
    window.scrollTo({
      top: targetScroll,
      behavior: 'auto'
    });
  } catch (error) {
    console.error('滚动错误:', error);
  }
};

// 重置游戏
const resetGame = () => {
  rocketPosition.value = startPosition;
  isLaunching.value = false;
  canLaunch.value = false;
  
  score.value = 0;
  gameTimeLeft.value = 30;
  isTimeUp.value = false;
  showRulesModal.value = true;
  
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }

  // 蓄力重置为初始状态
  resetCharging()
  
  // 重置时也滚动到底部
  scrollToBottom();

};

// 蓄力进度相关变量
const progress = ref(0); // 当前进度值(0-100)
const currentProgress = ref(0); // 显示的当前进度
const lastProgress = ref(null); // 上次松开时的进度
const isCharging = ref(false); // 是否正在蓄力
const direction = ref(1); // 进度方向: 1=增加, -1=减少
let intervalId = null; // 定时器ID

// 开始蓄力
const startCharging = () => {
  if (isCharging.value) return;
  if(isLaunching.value || isTimeUp.value || !gameStarted.value) return; // 发射过程中禁止蓄力

  console.log('开始蓄力')
  isCharging.value = true;
  lastProgress.value = null;

  // 启动定时器更新进度
  // 这里控制速度和精度：
  // 1. 30是定时器间隔时间(毫秒)，值越大速度越慢
  // 2. 1是每次进度变化量，值越小精度越高，速度也越慢
  intervalId = setInterval(() => {
    // 更新进度
    progress.value += direction.value * 2; // 每次变化1个单位（原先是2）

    // 到达边界时改变方向
    if (progress.value >= 100) {
      progress.value = 100;
      direction.value = -1;
    } else if (progress.value <= 0) {
      progress.value = 0;
      direction.value = 1;
    }

    // 更新显示的进度(四舍五入)
    currentProgress.value = Math.round(progress.value);
  }, 20); // 间隔时间改为30毫秒（原先是20）
};

// 停止蓄力
const stopCharging = () => {
  if (!isCharging.value) return;

  console.log('停止蓄力')
  startLaunch()
  isCharging.value = false;
  clearInterval(intervalId);

  // 记录松开时的进度
  lastProgress.value = Math.round(progress.value);
  
};

// 蓄力重置为初始状态
function resetCharging(){
  progress.value = 0;
  currentProgress.value = 0;
  direction.value = 1; // 重置方向为向上增长
}

// 初始化
onMounted(() => {
  window.addEventListener('resize', calculateViewport);
  calculateViewport();
  
  // 初始滚动到底部
  scrollToBottom();
});

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', calculateViewport);
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }

  //清除蓄力定时器
  if (intervalId) {
    clearInterval(intervalId);
  }
});
</script>

<style scoped lang="scss">
/* 保持原有样式不变 */
html, body {
  overflow: hidden !important;
  touch-action: none !important;
  margin: 0;
  padding: 0;
  font-family: 'Arial', sans-serif;
}

/* 得分信息区域 */
.score-info {
  position: fixed;
  top: 10px;
  left: 10px;
  width: 220px;
  height: 70px;
  background: url('@/assets/img/game/score.png') no-repeat center center;
  background-size: 100% auto;
  z-index: 10;
  .info-box {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .score {
    position: absolute;
    left: 133px;
    top: 17px;
    color: #FFFFFF;
    font-size: 14px;
    height: 23px;
    display: flex;
    align-items: flex-end;
    div {
      font-size: 26px;
      height: 23px;
      line-height: 24px;
      margin-right: 3px;
    }
  }
  .time {
    position: absolute;
    left: 128px;
    bottom: 0px;
    color: #FF5BA2;
    font-size: 14px;
    font-weight: bold;
    height: 23px;
    display: flex;
  }
}

// 规则弹框
.rules-modal {
  position: fixed;
  width: 90%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  .img1 {
    width: 100%;
    height: auto;
  }
  .img2 {
    width: 60px;
    height: auto;
    margin-top: 15px;
  }
}

.game-container {
  position: relative;
  height: 2000px;
  width: 100%;
  overflow: hidden;
}

.space-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("@/assets/img/game/trampoline_bg.png") repeat center center;
  background-size: contain;
  z-index: 1;
}

@keyframes twinkle {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

.character {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: auto;
  z-index: 10;
}

@keyframes flame {
  0% { height: 40px; width: 20px; }
  100% { height: 50px; width: 25px; }
}

.launch-button, .reset-button {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border: none;
  border-radius: 30px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  z-index: 100;
  transition: all 0.2s;
}

.launch-button {
  background-color: #2ecc71;
  color: white;
}

.launch-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: translateX(-50%) scale(1);
}

.launch-button:not(:disabled):hover {
  background-color: #27ae60;
  transform: translateX(-50%) scale(1.05);
}

.reset-button {
  background-color: #3498db;
  color: white;
}

.reset-button:hover {
  background-color: #2980b9;
  transform: translateX(-50%) scale(1.05);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  padding: 24px;
  border-radius: 10px;
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.modal h2 {
  color: #333;
  margin-top: 0;
}

.modal ul {
  text-align: left;
  margin: 20px 0;
  padding-left: 20px;
}

.modal li {
  margin-bottom: 8px;
}

.modal p {
  font-size: 18px;
  margin: 15px 0;
}

.modal button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.modal button:hover {
  background-color: #2980b9;
}

.countdown {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  .img1 {
    width: 200px;
    height: auto;
  }
  .img2 {
    width: auto;
    height: 180px;
  }
}


@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.3); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}
</style>
