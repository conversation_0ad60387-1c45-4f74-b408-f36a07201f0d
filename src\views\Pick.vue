<template>
  <div class="pick-bg">
    
      <TopLogo
        firstSrc="/img/TopLogo0.png"
        secondSrc="/img/TopLogo1.png"
        height="85px"
        transitionDuration="800"
      />
    <div
      class="container"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @mousedown="handleTouchStart"
      @mousemove="handleTouchMove"
      @mouseup="handleTouchEnd"
      @mouseleave="handleTouchEnd"
    >
      <!-- 圆盘背景图片 -->
      <img 
        src="/img/yuanpan.png" 
        alt="圆盘底座" 
        class="yuanpan-bg"
      />
      
      <div
        v-for="(card, idx) in visibleCards"
        :key="card.name"
        class="card"
        :class="{
          active: idx === 1,
          left: idx === 0,
          right: idx === 2,
        }"
        :style="{
          zIndex: 10 - Math.abs(idx - 1),
        }"
        @click="handleCardClick(idx)"
      >
        <div class="img">
          <img :src="card.img" :alt="card.name" />
        </div>
      </div>
    </div>
    <div class="pick-btn-wrap">
      <button class="pick-btn" @click="chooseCard(cards[activeIndex])">
        就它了
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import TopLogo from "@/components/TopLogo.vue";

// 路由实例（基于你的路由配置）
const router = useRouter();


// 1. 卡片与路由映射表（关联你的路由路径）
const routeMap = {
  zzz: "/gym", // zzz 对应 /gym（RhythmicGymnastics.vue）
  // 后续可扩展其他卡片：
  // '笑笑虾': '/xxx',
  // '呆呆豚': '/yyy',
};

// 2. 公仔数据,公仔对应的背景图片
const cards = [

  { name: "笑笑虾", img: "/img/niu.png", type: "tennis" },
  { name: "呆呆鱼", img: "/img/niu.png", type: "tennis" },
  { name: "酷酷蟹", img: "/img/niu.png", type: "trampoline" },
  { name: "萌萌蛙", img: "/img/niu.png", type: "trampoline" },
  { name: "憨憨贝", img: "/img/mgu.png", type: "archery" },
  { name: "可可牛", img: "/img/mgu.png", type: "gymnastics" },
  { name: "zzz", img: "/img/mgu.png" },
  { name: "yyy", img: "/img/mgu.png" },

];

// 3. 边框颜色
// const borderColors = [
//   "#eeb2d2",
//   "#f9d6b7",
//   "#b7e3f9",
//   "#f9f3b7",
//   "#f9c7b7",
//   "#d2b7f9",
//   "#b7f9d6",
// ];

// 4. 状态管理
const activeIndex = ref(2);
let timer = null;
let resumeTimer = null;
const isTransitioning = ref(false);
const touchStartX = ref(0);
const touchMoveX = ref(0);
const isDragging = ref(false);
const hasMoved = ref(false);

// 5. 计算可见的3个角色卡片
const visibleCards = computed(() => {
  const len = cards.length;
  const prevIndex = (activeIndex.value - 1 + len) % len;
  const nextIndex = (activeIndex.value + 1) % len;
  return [
    cards[prevIndex],
    cards[activeIndex.value],
    cards[nextIndex]
  ];
});



// 6. 自动轮播逻辑
function startAutoRotate() {
  if (timer) return;
  timer = setInterval(() => {
    if (!isTransitioning.value) nextCard();
  }, 3000);
}

function stopAutoRotate() {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  if (resumeTimer) {
    clearTimeout(resumeTimer);
    resumeTimer = null;
  }
}

// 7. 卡片切换
function prevCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value - 1 + cards.length) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 600);
}

function nextCard() {
  if (isTransitioning.value) return;
  isTransitioning.value = true;
  activeIndex.value = (activeIndex.value + 1) % cards.length;
  stopAutoRotate();
  setTimeout(() => {
    isTransitioning.value = false;
  }, 600);
}

// 8. 卡片点击处理
function handleCardClick(idx) {
  if (hasMoved.value) {
    hasMoved.value = false;
    return;
  }
  
  if (idx === 1) {
    // 点击中间卡片（当前活跃的）
    chooseCard(visibleCards.value[idx]);
  } else {
    // 点击左右卡片，切换到对应角色
    const len = cards.length;
    if (idx === 0) {
      // 点击左边卡片，切换到前一个
      activeIndex.value = (activeIndex.value - 1 + len) % len;
    } else if (idx === 2) {
      // 点击右边卡片，切换到后一个
      activeIndex.value = (activeIndex.value + 1) % len;
    }
    stopAutoRotate();
  }
}

// 9. 选择卡片（核心：路由跳转逻辑）
function chooseCard(card) {
  stopAutoRotate();
  const targetPath = routeMap[card.name];

  if (targetPath) {
    // 配置了路由：跳转对应页面（如zzz跳转到/gym）
    router.push(targetPath);
  } else {
    // 未配置路由：保留弹窗提示
    alert(`你选择了 ${card.name}，暂未配置游戏页面`);
  }
}

// 10. 滑动事件处理
function handleTouchStart(e) {
  touchStartX.value = e.type.includes("mouse")
    ? e.clientX
    : e.touches[0].clientX;
  isDragging.value = true;
  hasMoved.value = false;
  stopAutoRotate();
}

function handleTouchMove(e) {
  if (!isDragging.value) return;
  const currentX = e.type.includes("mouse") ? e.clientX : e.touches[0].clientX;
  if (Math.abs(currentX - touchStartX.value) > 5) {
    hasMoved.value = true;
  }
  touchMoveX.value = currentX;
}

function handleTouchEnd() {
  if (!isDragging.value) return;

  const diffX = touchMoveX.value - touchStartX.value;
  const threshold = 50;

  if (diffX > threshold) {
    prevCard();
  } else if (diffX < -threshold) {
    nextCard();
  }

  isDragging.value = false;
  setTimeout(() => {
    hasMoved.value = false;
  }, 100);
}

// 11. 生命周期
onMounted(() => {
  startAutoRotate();
});

onUnmounted(() => {
  stopAutoRotate();
  isDragging.value = false;
  hasMoved.value = false;
});

const bgStyle = computed(() => {
  const bg = cards[activeIndex.value]?.sportsbg || "";
  return {
    backgroundImage: bg
      ? `linear-gradient(180deg, #8ddfff 30%, #ffffff 70%), url(${bg})`
      : "linear-gradient(180deg, #8ddfff 30%, #ffffff 70%)",
    backgroundSize: bg ? "cover, contain" : "cover",
    backgroundRepeat: bg ? "no-repeat, no-repeat" : "no-repeat",
    backgroundPosition: bg ? "center center, center center" : "center center",
  };
});
</script>

<style scoped>
:root {
  --grid-gap: 1rem;
  --safe-area: 1rem;
  --bleed-margin: 0.5rem;
}

body {
  background: #f7f7fa;
}
.pick-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100svh;
  min-width: 100svw;
  background: url("/img/wangqiu01.png") no-repeat center center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
  padding: 0;
  margin: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
.sports-bg {
  position: absolute;
  z-index: 1;
  left: 0.1rem;
  right: 0.1rem;
  width: calc(100% - 0.2rem);
  height: 100%;
  object-fit: contain;
  pointer-events: none;
}
.pick-bg > *:not(.sports-bg) {
  position: relative;
  z-index: 2;
}
.pick-title {
  font-size: 2.1rem;
  font-weight: bold;
  color: #2d3a4b;
  margin-top: 3.2rem;
  margin-bottom: 8.2rem;
  text-align: center;
  letter-spacing: 0.08em;
  text-shadow: 0 2px 8px #fff8;
}
       /* 卡片 */
    .container {
      width: 100vw;
      height: 280px;
      max-width: 430px;
      min-height: 200px;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;
      justify-content: center;
      align-items: center;
      position: relative;
      margin: 10rem 1rem 5rem 1rem;
      padding: 0 1rem;
      box-sizing: border-box;
      perspective: 1000px;
      perspective-origin: 50% 50%;
      touch-action: pan-y;
    }




/* 圆盘背景图片 */
.yuanpan-bg {
  position: absolute;
  left: 50%;
  top: 110%;
  transform: translate(-50%, -50%);
  width: min(800px, 95vw);
  height: min(550px, 65vw);
  z-index: 2;
  pointer-events: none;
  object-fit: contain;
}

.card {
  position: absolute;
  left: 50%;
  top: 50%;
  width: min(220px, 30vw);
  height: min(280px, 38vw);
  background: transparent;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.7) rotateY(0deg) translateZ(0px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 8;
  backface-visibility: hidden;
  will-change: transform, opacity;
  user-select: none;
  cursor: pointer;
  overflow: hidden;
  box-shadow: none;
}
.card .img {
  width: 100%;
  height: 100%;
  margin: 0;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  overflow: hidden;
  background: transparent;
}
.card .img img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 不裁切图片 */
  object-position: center;
  border-radius: 0;
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.3s ease;
}

.card:hover .img img {
  transform: scale(1.05);
}

.card.active .img img {
  transform: scale(1.1);
}
.card-name {
  font-size: 1.2rem;
  color: #2d3a4b;
  font-weight: 600;
  margin-top: 2px;
  text-align: center;
  letter-spacing: 0.04em;
}
.card.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.8) rotateY(0deg) translateZ(200px) translateY(-15px);
  z-index: 10;
  box-shadow: none;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.4));
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.card.left,
.card.right {
  opacity: 1;
  transform: translate(-50%, -50%) scale(0.9) translateX(18vw) rotateY(0deg) translateZ(120px) translateY(15px);
  z-index: 9;

  box-shadow: none;
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.card.right {
  transform: translate(-50%, -50%) scale(0.9) translateX(-18vw) rotateY(0deg) translateZ(120px) translateY(15px);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

}


.pick-btn-wrap {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -4rem;
}
.pick-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff2985);
  border: none;
  outline: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  box-shadow: 0 8px 25px rgba(255, 41, 133, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 60px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
}

.pick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 41, 133, 0.4);
}

.pick-btn:active {
  transform: translateY(0);
  box-shadow: 0 6px 20px rgba(255, 41, 133, 0.3);
}

/* 大屏幕手机 (iPhone 12 Pro Max, Samsung Galaxy S21 Ultra 等) */
@media (min-width: 428px) and (max-width: 500px) {
  
  .container {
    height: 220px;
    min-height: 160px;
    margin: 14.5rem 1.25rem 5.5rem 1.25rem;
    padding: 0 1.5rem;
  }
  

  

  
  .yuanpan-bg {
    width: min(650px, 90vw);
    height: min(450px, 60vw);
  }
  
  .pick-btn-wrap {
    margin-top: -4.5rem;
  }
  
  .pick-btn {
    width: 180px;
    height: 55px;
    margin-top: 4rem;
    font-size: 1.1rem;
    padding: 0.8rem 2rem;
  }
  
  .card {
    width: min(170px, 28vw);
    height: min(220px, 35vw);
  }
  
  .card.active {
    transform: translate(-50%, -50%) scale(1.7) rotateY(0deg) translateZ(200px) translateY(-10px);
  }
  
  .card.active .img img {
    transform: scale(1.15);
  }
  
  .card.left,
  .card.right {
    transform: translate(-50%, -50%) scale(0.85) translateX(20vw) rotateY(0deg) translateZ(120px) translateY(28px);
  }
  
  .card.right {
    transform: translate(-50%, -50%) scale(0.85) translateX(-20vw) rotateY(0deg) translateZ(120px) translateY(28px);
  }
}

/* 中等屏幕手机 (iPhone 12, iPhone 13, Samsung Galaxy S21 等) */
@media (min-width: 390px) and (max-width: 427px) {
  
  .container {
    height: 200px;
    min-height: 140px;
    margin: 14.5rem 1.25rem 5.5rem 1.25rem;
    padding: 0 1.25rem;
  }
  

  
  .yuanpan-bg {
    width: min(580px, 92vw);
    height: min(400px, 62vw);
  }
  
  .pick-btn-wrap {
    margin-top: -4.2rem;
  }
  
  .pick-btn {
    width: 170px;
    height: 50px;
    margin-top: 5rem;
    font-size: 1rem;
    padding: 0.7rem 1.8rem;
  }
  
  .card {
    width: min(150px, 30vw);
    height: min(190px, 38vw);
  }
  
  .card.active {
    transform: translate(-50%, -50%) scale(1.65) rotateY(0deg) translateZ(190px) translateY(-8px);
  }
  
  .card.active .img img {
    transform: scale(1.12);
  }
  
  .card.left,
  .card.right {
    transform: translate(-50%, -50%) scale(0.82) translateX(22vw) rotateY(0deg) translateZ(110px) translateY(29px);
  }
  
  .card.right {
    transform: translate(-50%, -50%) scale(0.82) translateX(-22vw) rotateY(0deg) translateZ(110px) translateY(29px);
  }
}

/* 小屏幕手机 (iPhone SE, iPhone 12 mini 等) */
@media (max-width: 389px) {
  
  .container {
    height: 180px;
    min-height: 120px;

    margin: 14.5rem 1.25rem 5.5rem 1.25rem;
    padding: 0 1rem;

  }
  

  
  .yuanpan-bg {
    width: min(520px, 95vw);
    height: min(360px, 66vw);
  }
  
  .pick-btn-wrap {

    margin-top: -3.8rem;

  }
  
  .pick-btn {
    width: 150px;

    height: 45px;
    margin-top: 6.2rem;
    font-size: 0.9rem;
    padding: 0.6rem 1.5rem;

  }
  
  .card {
    width: min(140px, 32vw);
    height: min(180px, 42vw);
  }
  
  .card.active {
    transform: translate(-50%, -50%) scale(1.6) rotateY(0deg) translateZ(180px) translateY(-5px);
  }
  
  .card.active .img img {
    transform: scale(1.1);
  }
  
  .card.left,
  .card.right {

    transform: translate(-50%, -50%) scale(0.8) translateX(25vw) rotateY(0deg) translateZ(100px) translateY(30px);

  }
  
  .card.right {

    transform: translate(-50%, -50%) scale(0.8) translateX(-25vw) rotateY(0deg) translateZ(100px) translateY(30px);
  }
}

/* 超小屏幕手机 (iPhone SE 第一代等) */
@media (max-width: 375px) {
  
  .container {
    height: 160px;
    min-height: 100px;
    margin: 5.5rem 0.75rem 4.5rem 0.75rem;
    padding: 0 0.75rem;
  }
  
  .yuanpan-bg {
    width: min(450px, 98vw);
    height: min(310px, 70vw);
  }
  
  .pick-btn-wrap {
    margin-top: -3.5rem;
  }
  
  .pick-btn {
    width: 140px;
    height: 40px;
    margin-top: 7rem;
    font-size: 0.8rem;
    padding: 0.5rem 1.2rem;
  }
  
  .card {
    width: min(120px, 34vw);
    height: min(160px, 44vw);
  }
  
  .card.active {
    transform: translate(-50%, -50%) scale(1.5) rotateY(0deg) translateZ(170px) translateY(-2px);
  }
  
  .card.active .img img {
    transform: scale(1.08);
  }
  
  .card.left,
  .card.right {
    transform: translate(-50%, -50%) scale(0.75) translateX(28vw) rotateY(0deg) translateZ(90px) translateY(32px);
  }
  
  .card.right {
    transform: translate(-50%, -50%) scale(0.75) translateX(-28vw) rotateY(0deg) translateZ(90px) translateY(32px);
  }
}

/* 横屏模式适配 */
@media (max-height: 500px) and (orientation: landscape) {
  
  .container {
    height: 120px;
    min-height: 80px;
    margin: 3rem 0.5rem 2.5rem 0.5rem;
    padding: 0 0.5rem;
  }
  
  .yuanpan-bg {
    width: min(400px, 45vw);
    height: min(275px, 35vw);
  }
  
  .pick-btn-wrap {
    margin-top: -3rem;
  }
  
  .pick-btn {
    width: 120px;
    height: 35px;
    margin-top: 2rem;
    font-size: 0.7rem;
    padding: 0.4rem 1rem;
  }
  
  .card {
    width: min(100px, 25vw);
    height: min(130px, 32vw);
  }
  
  .card.active {
    transform: translate(-50%, -50%) scale(1.4) rotateY(0deg) translateZ(150px) translateY(5px);
  }
  
  .card.active .img img {
    transform: scale(1.05);
  }
  
  .card.left,
  .card.right {
    transform: translate(-50%, -50%) scale(0.7) translateX(30vw) rotateY(0deg) translateZ(80px) translateY(38px);
  }
  
  .card.right {
    transform: translate(-50%, -50%) scale(0.7) translateX(-30vw) rotateY(0deg) translateZ(80px) translateY(38px);

  }
}
</style>
