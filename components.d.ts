/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnimatedLogo: typeof import('./src/components/AnimatedLogo.vue')['default']
    ChargeBar: typeof import('./src/components/ChargeBar.vue')['default']
    GymJumpGame: typeof import('./src/components/GymJumpGame.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    Login: typeof import('./src/components/login.vue')['default']
    RedEnvelope: typeof import('./src/components/RedEnvelope.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    TLoading: typeof import('tdesign-mobile-vue')['Loading']
    TopLogo: typeof import('./src/components/TopLogo.vue')['default']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
