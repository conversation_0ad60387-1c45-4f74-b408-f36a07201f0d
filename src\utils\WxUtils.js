import {wxConfig} from "@/http/balanceApi"
// 设置微信配置
// let link = window.location.href.split('#')[0];
let link = 'https://gzm.easyart.cc';
console.log("link--",link)
let title = "公仔面游戏"
let desc = "公仔面游戏详情";
let imgUrl = "https://gzm.easyart.cc/img/logo.png"
const config = async ()=>{
    let config = await wxConfig(link)
    console.log("config---",config)
    let data = config.data;
    console.log("data---",data)
    wx.config({
        // 参考：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
        appId: data.appId,
        timestamp: data.timestamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
        debug: false,
        jsApiList: [
            "updateAppMessageShareData",
            "updateTimelineShareData"
        ]
    });
    wx.ready(function () {
        wx.updateAppMessageShareData({
            title: title,
            desc: desc,
            link: link,
            imgUrl: imgUrl,
            success: function () {
                // alert('分享成功');
            },
            cancel: function () {
                // alert('分享取消');
            },
            fail: function (err){
                console.log("updateAppMessageShareData--调用失败" + JSON.stringify(err))
            },
            trigger:function (){
                console.log("调用menu方法")
            }
        })
        wx.updateTimelineShareData({
            title, // 分享标题
            link, // 分享链接，该链接域名或路径必须与当前页面对应的服务号JS安全域名一致
            imgUrl, // 分享图标
            success: function () {
                // 设置成功
                // Toast("分享朋友圈成功---")
            },
            fail: function (err){
                console.log("updateTimelineShareData--调用失败" + JSON.stringify(err))
            },
            trigger:function (){
                console.log("调用menu方法")
            }
        })
    });
}
// 微信支付
const wxPayHandle = (data,callback)=>{
    wx.ready(function () {
        console.log("wx.ready")
        wx.checkJsApi({
            jsApiList: ['requestMerchantTransfer'],
            success: function (res) {
                if (res.checkResult['requestMerchantTransfer']) {
                    WeixinJSBridge.invoke('requestMerchantTransfer', {
                            // mchId: data.mchId,
                            // appId: data.appId,
                            // package: data.package
                            ...data,
                            package: data.packageInfo
                        },
                        function (res) {
                            if (res.err_msg === 'requestMerchantTransfer:ok') {
                                // res.err_msg将在页面展示成功后返回应用时返回success，并不代表付款成功
                                console.log("ok----")
                                // Toast("支付成功---")
                                if (callback) callback()
                            }
                        }
                    );
                } else {
                    alert('你的微信版本过低，请更新至最新版本。');
                }
            }
        });
    });
}
export {
    config,
    wxPayHandle
}