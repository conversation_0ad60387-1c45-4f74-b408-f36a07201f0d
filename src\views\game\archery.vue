<template>
  <div class="game-container"
    @mousedown="startCharging"
    @mouseup="stopCharging"
    @mouseleave="stopCharging"
    @touchstart="startCharging"
    @touchend="stopCharging">

    <!-- 得分信息显示 -->
    <div class="score-info"  v-show="!showRulesModal" >
      <div class="info-box">
        <div class="score">
          <div>{{ score }}</div><span>分</span>
        </div>
        <div class="time">{{ remainingTime }}s</div>
      </div>
    </div>
    
    <!-- 游戏主区域 -->
    <div class="game-area" v-show="!showRulesModal">
      <!-- 靶子 -->
      <img src="@/assets/img/game/archery/mark.png" class="mark" />
      
      <!-- 准星 -->
      <img src="@/assets/img/game/archery/crosshair.png" class="crosshair"
        :style="{ left: crosshairLeft + 'px', top: crosshairTop + 'px' }" />
      
      <!-- 箭矢轨迹 -->
      <svg v-if="showTrajectory" class="trajectory" :width="gameWidth" :height="gameHeight">
        <!-- <path 
          :d="trajectoryPath" 
          stroke="#ffd700" 
          stroke-width="2" 
          fill="none" 
          stroke-dasharray="4,4"
          class="trajectory-line"
        /> -->
      </svg>
      
      <!-- 飞行中的箭矢 -->
      <img src="@/assets/img/game/archery/arrow.png" class="arrow" v-if="arrowFlying" 
        :style="{ 
          left: arrowX + 'px', 
          top: arrowY + 'px',
          transform: `rotate(${arrowAngle}deg)`
        }" />
    </div>
    
    <!-- 弓箭 -->
    <div class="bow_area"  v-show="!showRulesModal"> 
      <div class="bow_box">
        <img src="@/assets/img/game/archery/bow.png" class="bow" />
        <img src="@/assets/img/game/archery/BowArrow.png" class="BowArrow" />
        <img src="@/assets/img/game/archery/hand.png" class="hand" />
      </div>
    </div>

    <!-- 游戏规则弹框 -->
    <div class="rules-modal" v-if="showRulesModal">
      <img src="@/assets/img/game/archery/5.png" class="img1" style="margin-bottom: 15px;" />
      <img src="@/assets/img/game/archery/1.png" class="img1" />
      <img src="@/assets/img/game/archery/2.png" class="img1" />
      <img src="@/assets/img/game/archery/3.png" class="img1" />
      <img src="@/assets/img/game/archery/4.png" class="img1" />
      <img src="@/assets/img/game/archery/close.png" class="img2" @click="startCountdown" />
    </div>

    <!-- 开始倒计时 -->
    <div v-if="showCountdown" class="countdown">
      <img src="@/assets/img/game/go.png" class="img1" v-if="countdownValue==0" />
      <img src="@/assets/img/game/one.png" class="img2" v-if="countdownValue==1" />
      <img src="@/assets/img/game/two.png" class="img2" v-if="countdownValue==2" />
      <img src="@/assets/img/game/three.png" class="img2" v-if="countdownValue==3" />
    </div>
    
    <!-- 游戏结束弹窗 -->
    <div v-if="gameOver" class="modal-overlay">
      <div class="modal">
        <h2 class="modal-title">游戏结束!</h2>
        <p class="modal-score">你的得分: {{ score }}</p>
        <button class="modal-restart" @click="restartGame">再来一次</button>
      </div>
    </div>
  </div>

  <ChargeBar :progress="progress"  v-show="!showRulesModal" />
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import ChargeBar from '@/components/ChargeBar.vue';

// 游戏状态
const showRulesModal = ref(true); // 显示规则弹窗
const showCountdown = ref(false); // 显示倒计时
const countdownValue = ref(3); // 倒计时数值
const gameStarted = ref(false); // 游戏是否开始

const score = ref(0);  //得分
const remainingTime = ref(30);  //游戏时间
const gameOver = ref(false);  // 游戏时间是否结束
const gameWidth = ref(0);     // 游戏区域宽度
const gameHeight = ref(0);    // 游戏区域高度

// 准星状态
const crosshairLeft = ref(100);
const crosshairTop = ref(168);
const crosshairSpeed = ref(3);
const crosshairDirection = ref(1); // 1: 向右, -1: 向左

// 箭矢状态
const arrowFlying = ref(false);  //箭是否在飞
const showTrajectory = ref(false);
const trajectoryPath = ref('');
const arrowX = ref(0);
const arrowY = ref(0);
const arrowAngle = ref(0);   //箭的便宜方向
const currentCharge = ref(0); // 记录当前发射时的蓄力值

// 计时器和动画帧
let gameTimer = null;
let crosshairInterval = null;

// 初始化游戏
onMounted(() => {
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

function initGameArea(){
  // 获取游戏区域尺寸
  const gameArea = document.querySelector('.game-area');
  if (gameArea) {
    gameWidth.value = gameArea.offsetWidth;
    gameHeight.value = gameArea.offsetHeight;
    
    // 初始化准星位置
    crosshairLeft.value = gameWidth.value / 4;
    //crosshairTop.value = (gameHeight.value / 2) - 50; // 中间偏上
  }
}

// 处理窗口大小变化
const handleResize = () => {
  const gameArea = document.querySelector('.game-area');
  if (gameArea) {
    gameWidth.value = gameArea.offsetWidth;
    gameHeight.value = gameArea.offsetHeight;
  }
};

// 准星移动逻辑
const startCrosshairMovement = () => {
  crosshairInterval = setInterval(() => {
    if (gameOver.value) return;
    
    // 更新准星位置
    crosshairLeft.value += crosshairSpeed.value * crosshairDirection.value;
    
    // 碰撞检测 - 边界反弹
    const crosshairSize = 40; // 准星图片宽度
    if (crosshairLeft.value <= 0) {
      crosshairDirection.value = 1;
    } else if (crosshairLeft.value >= gameWidth.value - crosshairSize) {
      crosshairDirection.value = -1;
    }
  }, 16); // 约60fps
};

// 开始倒计时
const startCountdown = () => {
  showRulesModal.value = false;
  showCountdown.value = true;
  nextTick(() => {
    // 确保游戏区域已渲染
    initGameArea();
  })
  countdownValue.value = 3;
  
  const countdownInterval = setInterval(() => {
    countdownValue.value--;
    
    if (countdownValue.value < 0) {
      clearInterval(countdownInterval);
      showCountdown.value = false;
      // 开始准星移动
      startCrosshairMovement();
      // 游戏倒计时
      startGameTimer();
    }
  }, 1000);
};

// 游戏倒计时
const startGameTimer = () => {
  gameTimer = setInterval(() => {
    remainingTime.value--;
    
    if (remainingTime.value <= 0) {
      endGame();
    }
  }, 1000);
};

// 射箭逻辑 - 核心修改：根据蓄力决定飞行距离
const shootArrow = () => {
  if (arrowFlying.value || gameOver.value) return;
  
  // 设置发射状态
  arrowFlying.value = true;
  
  // 获取弓箭位置（屏幕底部中央）
  const bow = document.querySelector('.bow');
  const bowRect = bow.getBoundingClientRect();
  const gameAreaRect = document.querySelector('.game-area').getBoundingClientRect();
  
  // 计算箭矢起始位置
  const startX = bowRect.left - gameAreaRect.left + bowRect.width / 2;
  const startY = bowRect.top - gameAreaRect.top;
  arrowX.value = startX;
  arrowY.value = startY;
  
  // 计算目标位置（准星中心）
  const targetX = crosshairLeft.value + 20; // 准星图片宽度40px，取中心
  const targetY = crosshairTop.value + 20;
  
  // 根据蓄力决定实际飞行距离
  let actualTargetX, actualTargetY;
  if (currentCharge.value < 50) {
    // 蓄力不足50%，只能飞到一半距离
    actualTargetX = startX + (targetX - startX) * 0.5;
    actualTargetY = startY + (targetY - startY) * 0.5;
  } else {
    // 蓄力足够，可到达目标
    actualTargetX = targetX;
    actualTargetY = targetY;
  }
  
  // 计算箭矢角度
  const deltaX = actualTargetX - startX;
  const deltaY = actualTargetY - startY;
  const rad = Math.atan2(deltaY, deltaX);
  arrowAngle.value = rad * (180 / Math.PI) + 90;
  
  // 计算轨迹
  calculateTrajectory(startX, startY, actualTargetX, actualTargetY);
  showTrajectory.value = true;
  
  // 箭矢飞行动画
  const flightDuration = currentCharge.value < 50 ? 300 : 400; // 蓄力不足时飞行时间更短
  const steps = 30;
  const interval = flightDuration / steps;
  
  // 每步移动距离
  const stepX = (actualTargetX - startX) / steps;
  const stepY = (actualTargetY - startY) / steps;
  
  let currentStep = 0;
  const flightInterval = setInterval(() => {
    currentStep++;
    
    // 更新箭矢位置
    arrowX.value += stepX;
    arrowY.value += stepY;
    
    // 实时更新角度
    const currentDeltaX = actualTargetX - arrowX.value;
    const currentDeltaY = actualTargetY - arrowY.value;
    const currentRad = Math.atan2(currentDeltaY, currentDeltaX);
    arrowAngle.value = currentRad * (180 / Math.PI) + 90;
    
    // 检查是否到达目标
    if (currentStep >= steps) {
      clearInterval(flightInterval);
      arrowFlying.value = false;
      showTrajectory.value = false;
      
      // 只有蓄力足够且命中才得分
      if (currentCharge.value >= 50) {
        checkHit(targetX, targetY);
      }
    }
  }, interval);
};

// 计算箭矢轨迹（抛物线）
const calculateTrajectory = (startX, startY, endX, endY) => {
  const controlX = (startX + endX) / 2;
  const controlY = Math.min(startY, endY) - Math.abs(startX - endX) * 0.2;
  trajectoryPath.value = `M ${startX} ${startY} Q ${controlX} ${controlY}, ${endX} ${endY}`;
};

// 检查是否命中靶区
const checkHit = (x, y) => {
  const target = document.querySelector('.mark');
  const targetRect = target.getBoundingClientRect();
  const gameAreaRect = document.querySelector('.game-area').getBoundingClientRect();
  
  const targetCenterX = (targetRect.left - gameAreaRect.left) + targetRect.width / 2;
  const targetCenterY = (targetRect.top - gameAreaRect.top) + targetRect.height / 2;
  const targetRadius = targetRect.width / 2;
  
  const distance = Math.sqrt(
    Math.pow(x - targetCenterX, 2) +
    Math.pow(y - targetCenterY, 2)
  );
  
  if (distance < targetRadius) {
    score.value++;
  }
};

// 结束游戏
const endGame = () => {
  gameOver.value = true;
  clearInterval(gameTimer);
  clearInterval(crosshairInterval);
};

// 重新开始游戏
const restartGame = () => {
  score.value = 0;
  remainingTime.value = 30;
  gameOver.value = false;
  startCrosshairMovement();
  startGameTimer();
};

// 蓄力进度相关
const progress = ref(0);
const currentProgress = ref(0);
const lastProgress = ref(null);
const isCharging = ref(false);
const direction = ref(1);
let intervalId = null;

// 开始蓄力
const startCharging = () => {
  if (showRulesModal.vallue || showCountdown.value || isCharging.value || arrowFlying.value || gameOver.value) return;

  isCharging.value = true;
  intervalId = setInterval(() => {
    progress.value += direction.value * 2;
    
    if (progress.value >= 100) {
      progress.value = 100;
      direction.value = -1;
    } else if (progress.value <= 0) {
      progress.value = 0;
      direction.value = 1;
    }
    
    currentProgress.value = Math.round(progress.value);
  }, 20);
};

// 停止蓄力 - 记录蓄力值
const stopCharging = () => {
  if (!isCharging.value) return;

  currentCharge.value = Math.round(progress.value);
  shootArrow();
  isCharging.value = false;
  clearInterval(intervalId);
  lastProgress.value = currentCharge.value;
  resetCharging();
};

// 重置蓄力
function resetCharging() {
  progress.value = 0;
  currentProgress.value = 0;
  direction.value = 1;
}

// 组件卸载时清理
onUnmounted(() => {
  clearInterval(gameTimer);
  clearInterval(crosshairInterval);
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
/* 游戏容器 */
.game-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: url('@/assets/img/game/trampoline_bg.png') no-repeat center center;
  background-size: cover;
}

.countdown {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  .img1 {
    width: 200px;
    height: auto;
  }
  .img2 {
    width: auto;
    height: 180px;
  }
}

/* 游戏信息区域 */
.score-info {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 220px;
  height: 70px;
  background: url('@/assets/img/game/score.png') no-repeat center center;
  background-size: 100% auto;
  z-index: 10;
  .info-box {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .score {
    position: absolute;
    left: 133px;
    top: 17px;
    color: #FFFFFF;
    font-size: 14px;
    height: 23px;
    display: flex;
    align-items: flex-end;
    div {
      font-size: 26px;
      height: 23px;
      line-height: 24px;
      margin-right: 3px;
    }
  }
  .time {
    position: absolute;
    left: 128px;
    bottom: 0px;
    color: #FF5BA2;
    font-size: 14px;
    font-weight: bold;
    height: 23px;
    display: flex;
  }
}

/* 游戏主区域 */
.game-area {
  position: relative;
  width: 70%;
  height: calc(100% - 235px);
  overflow: hidden;
  margin: 0 auto;
  .mark {
    position: absolute;
    width: 100px;
    height: auto;
    top: 140px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .crosshair {
    position: absolute;
    width: 40px;
    height: auto;
    pointer-events: none;
    z-index: 5;
  }
}

/* 弓箭样式 */
.bow_area {
  width: 100%;
  height: 235px;
  position: absolute;
  bottom: 0;
  left: 0;
  pointer-events: none;
  z-index: 10;
  display: flex;
  justify-content: center;
  .bow_box {
    width: 290px;
    height: 100%;
    position: relative;
    .bow {
      width: 290px;
      height: auto;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
    .BowArrow {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: auto;
    }
    .hand {
      position: absolute;
      bottom: 44px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: auto;
    }
  }
}

/* 箭矢样式 */
.arrow {
  position: absolute;
  width: 20px;
  height: auto;
  transform-origin: center;
  z-index: 8;
}

/* 轨迹样式 */
.trajectory {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 7;
}

.trajectory-line {
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: 16;
  }
}

// 规则弹框
.rules-modal {
  position: fixed;
  width: 90%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  .img1 {
    width: 100%;
    height: auto;
  }
  .img2 {
    width: 60px;
    height: auto;
    margin-top: 15px;
  }
}

/* 游戏结束弹窗 */
.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.modal {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  width: 80%;
  max-width: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-title {
  font-size: 24px;
  margin-bottom: 15px;
  color: #333;
}

.modal-score {
  font-size: 18px;
  margin-bottom: 25px;
  color: #666;
}

.modal-restart {
  padding: 10px 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.modal-restart:hover {
  background-color: #2980b9;
}

.hide {
  display: none !important;
}
</style>